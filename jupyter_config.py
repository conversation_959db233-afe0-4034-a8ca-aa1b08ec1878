# Jupyter configuration for Harmattan
# This file disables authentication and allows iframe embedding

c = get_config()

# Disable authentication (using newer ServerApp settings)
c.ServerApp.token = ''
c.ServerApp.password = ''
c.ServerApp.disable_check_xsrf = True

# Allow all origins and iframe embedding
c.ServerApp.allow_origin = '*'
c.ServerApp.allow_remote_access = True

# Disable browser opening
c.ServerApp.open_browser = False

# Allow root (for Docker/development)
c.ServerApp.allow_root = True

# Set IP to listen on all interfaces
c.ServerApp.ip = '0.0.0.0'

# Allow iframe embedding by setting proper headers
c.ServerApp.tornado_settings = {
    'headers': {
        'Content-Security-Policy': "frame-ancestors 'self' *",
        'X-Frame-Options': 'ALLOWALL'
    }
}

# Also set legacy NotebookApp settings for compatibility
c.NotebookApp.token = ''
c.NotebookApp.password = ''
c.NotebookApp.disable_check_xsrf = True
c.NotebookApp.allow_origin = '*'
c.NotebookApp.allow_remote_access = True
c.NotebookApp.open_browser = False
c.NotebookApp.allow_root = True
c.NotebookApp.ip = '0.0.0.0'
