"""
Simple database manager for testing without DuckDB dependency
"""
import os
import pandas as pd
from pathlib import Path
import time

class DatabaseManager:
    """Simple database manager for testing"""
    
    def __init__(self, db_path='dbs/harmattan.db'):
        self.db_path = db_path
        self.dbs_dir = Path('dbs')
        self.dbs_dir.mkdir(exist_ok=True)
    
    def list_tables(self):
        """List all tables (parquet files) in the database"""
        try:
            parquet_files = list(self.dbs_dir.glob('*.parquet'))
            tables = []
            
            for file_path in parquet_files:
                table_name = file_path.stem
                try:
                    # Get basic file info
                    file_size = file_path.stat().st_size
                    
                    # Try to get row count from parquet file
                    try:
                        df = pd.read_parquet(file_path)
                        row_count = len(df)
                    except:
                        row_count = 'Unknown'
                    
                    tables.append({
                        'name': table_name,
                        'file_path': str(file_path),
                        'row_count': row_count,
                        'file_size': file_size,
                        'file_size_mb': round(file_size / (1024 * 1024), 2)
                    })
                except Exception as e:
                    tables.append({
                        'name': table_name,
                        'file_path': str(file_path),
                        'row_count': 'Error',
                        'file_size': file_path.stat().st_size if file_path.exists() else 0,
                        'file_size_mb': 0,
                        'error': str(e)
                    })
            
            return sorted(tables, key=lambda x: x['name'])
        except Exception as e:
            print(f"Error listing tables: {e}")
            return []
    
    def add_table_from_file(self, file_path, table_name):
        """Convert uploaded file to parquet"""
        try:
            file_ext = Path(file_path).suffix.lower()
            
            # Read file based on extension
            if file_ext == '.csv':
                df = pd.read_csv(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                # Handle Excel files
                excel_file = pd.ExcelFile(file_path)
                if len(excel_file.sheet_names) == 1:
                    df = pd.read_excel(file_path)
                    self._save_as_parquet(df, table_name)
                else:
                    # Multiple sheets
                    for sheet_name in excel_file.sheet_names:
                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                        sheet_table_name = f"{table_name}_{sheet_name.replace(' ', '_').lower()}"
                        self._save_as_parquet(df, sheet_table_name)
                return True
            elif file_ext == '.parquet':
                df = pd.read_parquet(file_path)
            else:
                raise Exception(f"Unsupported file format: {file_ext}")
            
            # Save as parquet if not Excel with multiple sheets
            if file_ext not in ['.xlsx', '.xls'] or len(pd.ExcelFile(file_path).sheet_names) == 1:
                self._save_as_parquet(df, table_name)
            
            return True
            
        except Exception as e:
            print(f"Error adding table from file: {e}")
            return False
    
    def _save_as_parquet(self, df, table_name):
        """Save DataFrame as parquet file"""
        # Clean column names
        df.columns = [col.strip().lower().replace(' ', '_') for col in df.columns]
        
        # Save as parquet
        parquet_path = self.dbs_dir / f"{table_name}.parquet"
        df.to_parquet(parquet_path, index=False)
    
    def execute_query(self, query):
        """Execute SQL query (simplified version)"""
        start_time = time.time()
        try:
            # For now, return a simple message
            execution_time = round(time.time() - start_time, 3)
            
            return {
                'success': False,
                'error': 'DuckDB not available. Please install DuckDB to use SQL queries.',
                'execution_time': execution_time
            }
                
        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }
    
    def get_table_info(self, table_name):
        """Get information about a specific table"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if not parquet_path.exists():
                return None
            
            # Read parquet file to get info
            df = pd.read_parquet(parquet_path)
            
            columns = [{'name': col, 'type': str(df[col].dtype)} for col in df.columns]
            
            return {
                'name': table_name,
                'columns': columns,
                'row_count': len(df),
                'file_size': parquet_path.stat().st_size
            }
                
        except Exception as e:
            print(f"Error getting table info: {e}")
            return None
    
    def get_sample_data(self, table_name, limit=100):
        """Get sample data from a table"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if not parquet_path.exists():
                return None
            
            df = pd.read_parquet(parquet_path)
            sample_df = df.head(limit)
            
            return {
                'columns': list(sample_df.columns),
                'data': sample_df.values.tolist()
            }
                
        except Exception as e:
            print(f"Error getting sample data: {e}")
            return None
    
    def get_table_schema(self, table_name):
        """Get schema information for a table"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if not parquet_path.exists():
                return None
            
            df = pd.read_parquet(parquet_path)
            schema = [{'column': col, 'type': str(df[col].dtype)} for col in df.columns]
            
            return schema
                
        except Exception as e:
            print(f"Error getting table schema: {e}")
            return None
    
    def delete_table(self, table_name):
        """Delete a table (remove parquet file)"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if parquet_path.exists():
                parquet_path.unlink()
                return True
            return False
        except Exception as e:
            print(f"Error deleting table: {e}")
            return False
