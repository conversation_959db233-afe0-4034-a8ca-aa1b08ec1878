from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
import os
from werkzeug.utils import secure_filename
from pathlib import Path

# Initialize Flask app
app = Flask(__name__, template_folder='views', static_folder='assets')
app.config['SECRET_KEY'] = 'your-secret-key-here'  # Change this in production
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # Disable caching for development

# Ensure required directories exist
os.makedirs('uploads', exist_ok=True)
os.makedirs('dbs', exist_ok=True)
os.makedirs('tmps', exist_ok=True)

# Register blueprints
from routes.auth import auth_bp
from routes.dashboard import dashboard_bp
from routes.sql_client import sql_client_bp
from routes.notebooks import notebooks_bp

app.register_blueprint(auth_bp)
app.register_blueprint(dashboard_bp)
app.register_blueprint(sql_client_bp)
app.register_blueprint(notebooks_bp)

# Root route redirect
@app.route('/')
def index():
    if 'user' in session:
        return redirect(url_for('dashboard.index'))
    else:
        return redirect(url_for('auth.login'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)