#!/usr/bin/env python3
"""Test Flask app import and basic functionality"""

try:
    print("Testing Flask app import...")
    from app import app
    print("✓ Flask app imported successfully!")
    
    print("\nAvailable routes:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.rule} -> {rule.endpoint}")
    
    print(f"\nTemplate folder: {app.template_folder}")
    print(f"Static folder: {app.static_folder}")
    
    # Test if we can create a test client
    with app.test_client() as client:
        print("\n✓ Test client created successfully!")
        
        # Test root route redirect
        response = client.get('/')
        print(f"Root route status: {response.status_code}")
        
        # Test login page
        response = client.get('/auth/login')
        print(f"Login page status: {response.status_code}")
        
    print("\n✅ All tests passed! Flask app is working correctly.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
