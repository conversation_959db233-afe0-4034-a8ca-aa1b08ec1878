{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Test Notebook\n", "\n", "This is a simple test notebook to verify the sidebar functionality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Hello from Harmattan!\")\n", "import pandas as pd\n", "print(f\"Pandas version: {pd.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Analysis Example\n", "\n", "This notebook can be used to test the Ju<PERSON><PERSON> integration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample data\n", "import numpy as np\n", "data = np.random.randn(100)\n", "df = pd.DataFrame({'values': data})\n", "print(df.describe())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}