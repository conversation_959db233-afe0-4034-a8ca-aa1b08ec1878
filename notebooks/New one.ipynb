{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# New one\n", "\n", "This notebook was created in Harmattan Analytics Platform.\n", "\n", "## Getting Started\n", "\n", "You can access your Harmattan database using:\n", "\n", "```python\n", "import duckdb\n", "conn = duckdb.connect('../dbs/harmattan.db')\n", "df = conn.execute('SELECT * FROM your_table').df()\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "print('Welcome to Harmattan Analytics!')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}