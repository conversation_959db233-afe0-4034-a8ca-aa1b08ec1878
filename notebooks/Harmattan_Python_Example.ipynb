{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Harmattan Analytics - Python Example\n", "\n", "This notebook demonstrates how to access and analyze data from the Harmattan database using Python."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import duckdb\n", "from pathlib import Path\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette('husl')\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Connect to Harmattan Database"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Connect to the Harmattan DuckDB database\n", "db_path = '../dbs/harmattan.db'\n", "conn = duckdb.connect(db_path)\n", "\n", "print(f\"Connected to database: {db_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. List Available Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List all parquet files (tables) in the database\n", "dbs_dir = Path('../dbs')\n", "parquet_files = list(dbs_dir.glob('*.parquet'))\n", "\n", "print(\"Available tables:\")\n", "for file_path in parquet_files:\n", "    table_name = file_path.stem\n", "    print(f\"  - {table_name}\")\n", "    \n", "    # Register as view in DuckDB\n", "    conn.execute(f\"CREATE OR REPLACE VIEW {table_name} AS SELECT * FROM '{file_path}'\")\n", "\n", "if not parquet_files:\n", "    print(\"  No tables found. Upload some data files first!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Example Data Analysis\n", "\n", "Replace 'your_table_name' with an actual table name from your database."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Load data from a table\n", "# Replace 'your_table_name' with an actual table name\n", "table_name = 'your_table_name'  # Change this!\n", "\n", "try:\n", "    # Query data using DuckDB\n", "    df = conn.execute(f\"SELECT * FROM {table_name} LIMIT 1000\").df()\n", "    \n", "    print(f\"Loaded {len(df)} rows from {table_name}\")\n", "    print(f\"Columns: {list(df.columns)}\")\n", "    \n", "    # Display basic info\n", "    print(\"\\nDataset Info:\")\n", "    print(df.info())\n", "    \n", "    # Display first few rows\n", "    print(\"\\nFirst 5 rows:\")\n", "    display(df.head())\n", "    \n", "except Exception as e:\n", "    print(f\"Error: {e}\")\n", "    print(\"Please replace 'your_table_name' with an actual table name from your database.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Visualization Example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example visualization (modify based on your data)\n", "try:\n", "    if 'df' in locals() and not df.empty:\n", "        # Get numeric columns\n", "        numeric_cols = df.select_dtypes(include=[np.number]).columns\n", "        \n", "        if len(numeric_cols) > 0:\n", "            # Create a simple histogram for the first numeric column\n", "            plt.figure(figsize=(10, 6))\n", "            plt.hist(df[numeric_cols[0]].dropna(), bins=30, alpha=0.7)\n", "            plt.title(f'Distribution of {numeric_cols[0]}')\n", "            plt.xlabel(numeric_cols[0])\n", "            plt.ylabel('Frequency')\n", "            plt.show()\n", "            \n", "            # Correlation matrix if multiple numeric columns\n", "            if len(numeric_cols) > 1:\n", "                plt.figure(figsize=(10, 8))\n", "                correlation_matrix = df[numeric_cols].corr()\n", "                sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)\n", "                plt.title('Correlation Matrix')\n", "                plt.show()\n", "        else:\n", "            print(\"No numeric columns found for visualization.\")\n", "    else:\n", "        print(\"No data loaded. Please load a table first.\")\n", "        \n", "except Exception as e:\n", "    print(f\"Visualization error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Advanced SQL Queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example of more complex SQL queries\n", "try:\n", "    # Example: Aggregation query\n", "    query = f\"\"\"\n", "    SELECT \n", "        COUNT(*) as total_rows,\n", "        COUNT(DISTINCT *) as unique_rows\n", "    FROM {table_name}\n", "    \"\"\"\n", "    \n", "    result = conn.execute(query).df()\n", "    print(\"Query Results:\")\n", "    display(result)\n", "    \n", "except Exception as e:\n", "    print(f\"Query error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Export processed data\n", "try:\n", "    if 'df' in locals() and not df.empty:\n", "        # Save to CSV\n", "        output_file = f'{table_name}_processed.csv'\n", "        df.to_csv(output_file, index=False)\n", "        print(f\"Data exported to {output_file}\")\n", "        \n", "        # Basic statistics\n", "        print(\"\\nBasic Statistics:\")\n", "        display(df.describe())\n", "    else:\n", "        print(\"No data to export.\")\n", "        \n", "except Exception as e:\n", "    print(f\"Export error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "1. Replace `your_table_name` with actual table names from your database\n", "2. Modify the analysis code based on your specific data structure\n", "3. Add more sophisticated visualizations and statistical analysis\n", "4. Use the SQL Client in Harmattan for ad-hoc queries\n", "5. Upload more data files through the Dashboard\n", "\n", "Happy analyzing! 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}