{% extends "base/harmattan.html" %}

{% block title %}SQL Client - Harmattan{% endblock %}

{% block header %}
<small class="text-muted">Query your data</small>
<h1 class="h4 mt-1">SQL Client</h1>
{% endblock %}

{% block header_actions %}
<button type="button" class="btn btn-success" id="runQueryBtn">
    <i class="fa fa-play me-1"></i> Run Query
</button>
<button type="button" class="btn btn-outline-secondary" id="clearBtn">
    <i class="fa fa-eraser me-1"></i> Clear
</button>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<style>
.sql-editor {
    height: 50vh;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.CodeMirror {
    height: 100%;
    font-size: 14px;
}
.results-panel {
    height: 45vh;
    overflow: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}
.query-info {
    background: #f8f9fa;
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    font-size: 12px;
}
.table-container {
    overflow: auto;
    max-height: calc(45vh - 40px);
}
.results-table {
    font-size: 12px;
}
.results-table th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}
.error-message {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px;
    margin: 12px;
}
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- SQL Editor -->
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">SQL Editor</h6>
                <div>
                    <small class="text-muted me-3">Press Ctrl+Enter to run query</small>
                    <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#helpModal">
                        <i class="fa fa-question-circle"></i> Help
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="sql-editor">
                    <textarea id="sqlEditor" placeholder="-- Enter your SQL query here
-- Example: SELECT * FROM your_table_name LIMIT 10;"></textarea>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Query Results</h6>
            </div>
            <div class="card-body p-0">
                <div class="results-panel">
                    <div id="resultsContent" class="text-center py-5 text-muted">
                        <i class="fa fa-code fa-2x mb-3"></i>
                        <p>Run a SQL query to see results here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">SQL Client Help</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Available Tables:</h6>
                <div class="mb-3">
                    {% if tables %}
                        {% for table in tables %}
                        <span class="badge bg-secondary me-1 mb-1">{{ table.name }}</span>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No tables available. Upload data first.</p>
                    {% endif %}
                </div>
                
                <h6>Example Queries:</h6>
                <div class="mb-3">
                    <pre class="bg-light p-2 rounded"><code>-- View all data from a table
SELECT * FROM table_name LIMIT 100;

-- Count rows
SELECT COUNT(*) FROM table_name;

-- Group by example
SELECT column_name, COUNT(*) 
FROM table_name 
GROUP BY column_name;

-- Join tables
SELECT a.*, b.column_name
FROM table1 a
JOIN table2 b ON a.id = b.id;</code></pre>
                </div>

                <h6>Keyboard Shortcuts:</h6>
                <ul>
                    <li><kbd>Ctrl+Enter</kbd> - Run query</li>
                    <li><kbd>Ctrl+A</kbd> - Select all</li>
                    <li><kbd>Ctrl+/</kbd> - Toggle comment</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/sql/sql.min.js"></script>
<script>
// Initialize CodeMirror
const editor = CodeMirror.fromTextArea(document.getElementById('sqlEditor'), {
    mode: 'text/x-sql',
    theme: 'default',
    lineNumbers: true,
    lineWrapping: true,
    indentUnit: 2,
    smartIndent: true,
    extraKeys: {
        'Ctrl-Enter': runQuery,
        'Cmd-Enter': runQuery
    }
});

// Run query function
function runQuery() {
    const query = editor.getSelection() || editor.getValue();
    
    if (!query.trim()) {
        alert('Please enter a SQL query');
        return;
    }

    // Show loading
    document.getElementById('resultsContent').innerHTML = `
        <div class="loading">
            <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
            <p>Executing query...</p>
        </div>
    `;

    // Execute query
    fetch('/sql/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: query })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayResults(data);
        } else {
            displayError(data.error);
        }
    })
    .catch(error => {
        displayError('Network error: ' + error.message);
    });
}

function displayResults(data) {
    const { columns, data: rows, row_count, execution_time } = data;
    
    let html = `
        <div class="query-info">
            <strong>${row_count}</strong> rows returned in <strong>${execution_time}s</strong>
        </div>
    `;

    if (rows.length > 0) {
        html += `
            <div class="table-container">
                <table class="table table-sm table-striped results-table mb-0">
                    <thead>
                        <tr>
                            ${columns.map(col => `<th>${col}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${rows.map(row => `
                            <tr>
                                ${row.map(cell => `<td>${cell !== null ? cell : '<em>NULL</em>'}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">Query executed successfully but returned no rows.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function displayError(error) {
    document.getElementById('resultsContent').innerHTML = `
        <div class="error-message">
            <strong>Error:</strong> ${error}
        </div>
    `;
}

// Event listeners
document.getElementById('runQueryBtn').addEventListener('click', runQuery);
document.getElementById('clearBtn').addEventListener('click', () => {
    editor.setValue('');
    document.getElementById('resultsContent').innerHTML = `
        <div class="text-center py-5 text-muted">
            <i class="fa fa-code fa-2x mb-3"></i>
            <p>Run a SQL query to see results here</p>
        </div>
    `;
});

// Set focus to editor
editor.focus();
</script>
{% endblock %}
