{% extends "base/harmattan.html" %}

{% block title %}JupyterLite Notebooks - Harmattan{% endblock %}

{% block extra_css %}
<style>
.status-indicator {
    display: inline-block;
}

/* JupyterLite integration styles */
#jupyterlite-container {
    width: 100%;
    height: calc(100vh - 120px);
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.jupyterlite-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.jupyterlite-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.kernel-status {
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.kernel-available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.kernel-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.launch-btn {
    transition: all 0.3s ease;
}

.launch-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
</style>
{% endblock %}

{% block header %}
<small class="text-muted">Interactive computing with JupyterLite</small>
<h1 class="h4 mt-1">Jupyter Notebooks</h1>
{% endblock %}

{% block header_actions %}
<button type="button" class="btn btn-success launch-btn" onclick="launchJupyterLite()">
    <i class="fa fa-rocket me-1"></i> Launch JupyterLite
</button>
<button type="button" class="btn btn-outline-info" onclick="refreshNotebookList()">
    <i class="fa fa-refresh me-1"></i> Refresh Files
</button>
{% endblock %}

{% block content %}
<!-- JupyterLite Status Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card jupyterlite-card">
            <div class="card-header jupyterlite-header">
                <h5 class="mb-0">
                    <i class="fa fa-flask me-2"></i>
                    JupyterLite - Browser-Based Notebooks
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Available Kernels</h6>
                        <div class="mb-2">
                            <span class="kernel-status kernel-available">
                                <i class="fa fa-python me-1"></i> Python (Pyodide)
                            </span>
                        </div>
                        <div class="mb-2">
                            <span class="kernel-status kernel-pending">
                                <i class="fa fa-bar-chart me-1"></i> R (Coming Soon)
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Features</h6>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-success me-2"></i> No server required</li>
                            <li><i class="fa fa-check text-success me-2"></i> Runs entirely in browser</li>
                            <li><i class="fa fa-check text-success me-2"></i> Full Python ecosystem</li>
                            <li><i class="fa fa-check text-success me-2"></i> Seamless integration</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-primary launch-btn me-2" onclick="launchJupyterLite()">
                        <i class="fa fa-rocket me-1"></i> Launch JupyterLite
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="openInNewTab()">
                        <i class="fa fa-external-link me-1"></i> Open in New Tab
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Embedded JupyterLite Container -->
<div id="embedded-container" class="row" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fa fa-flask me-2"></i>
                    JupyterLite Notebook Environment
                </h6>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFullscreen()">
                        <i class="fa fa-expand" id="fullscreen-icon"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="closeEmbedded()">
                        <i class="fa fa-times"></i> Close
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <iframe id="jupyterlite-container" 
                        src="" 
                        style="width: 100%; height: calc(100vh - 200px); border: none;">
                </iframe>
            </div>
        </div>
    </div>
</div>

<!-- Quick Start Guide -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fa fa-info-circle me-2"></i>
                    Getting Started with JupyterLite
                </h6>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Launch JupyterLite:</strong> Click the "Launch JupyterLite" button above</li>
                    <li><strong>Create a notebook:</strong> Click "New" → "Python (Pyodide)" in JupyterLite</li>
                    <li><strong>Access your data:</strong> Use the file browser to access your notebooks folder</li>
                    <li><strong>Install packages:</strong> Use <code>%pip install package_name</code> in cells</li>
                </ol>
                
                <h6 class="mt-3">Sample Code to Get Started:</h6>
                <pre class="bg-light p-2 rounded"><code>import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Create sample data
data = np.random.randn(100)
df = pd.DataFrame({'values': data})

# Plot the data
plt.figure(figsize=(10, 6))
plt.plot(df.index, df['values'])
plt.title('Sample Data Visualization')
plt.show()

print(f"Data shape: {df.shape}")
print(df.describe())</code></pre>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fa fa-lightbulb-o me-2"></i>
                    Tips & Tricks
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <strong>Package Installation:</strong><br>
                        <small class="text-muted">Use <code>%pip install</code> or <code>import micropip; await micropip.install('package')</code></small>
                    </li>
                    <li class="mb-2">
                        <strong>File Access:</strong><br>
                        <small class="text-muted">Your notebooks are automatically synced with the notebooks folder</small>
                    </li>
                    <li class="mb-2">
                        <strong>Performance:</strong><br>
                        <small class="text-muted">First load may take a moment as Python environment initializes</small>
                    </li>
                    <li class="mb-2">
                        <strong>Persistence:</strong><br>
                        <small class="text-muted">Notebooks are saved locally in your browser</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let statusModal;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any modals if needed
    checkJupyterLiteStatus();
});

function launchJupyterLite() {
    const container = document.getElementById('embedded-container');
    const iframe = document.getElementById('jupyterlite-container');
    
    // Set the iframe source to JupyterLite
    iframe.src = '/static/jupyterlite/lab/index.html';
    
    // Show the embedded container
    container.style.display = 'block';
    
    // Scroll to the container
    container.scrollIntoView({ behavior: 'smooth' });
}

function openInNewTab() {
    window.open('/static/jupyterlite/lab/index.html', '_blank');
}

function closeEmbedded() {
    const container = document.getElementById('embedded-container');
    const iframe = document.getElementById('jupyterlite-container');
    
    // Hide container and clear iframe
    container.style.display = 'none';
    iframe.src = '';
}

function toggleFullscreen() {
    const iframe = document.getElementById('jupyterlite-container');
    const icon = document.getElementById('fullscreen-icon');
    
    if (iframe.style.height === '95vh') {
        iframe.style.height = 'calc(100vh - 200px)';
        icon.className = 'fa fa-expand';
    } else {
        iframe.style.height = '95vh';
        icon.className = 'fa fa-compress';
    }
}

function checkJupyterLiteStatus() {
    // JupyterLite is always ready since it's client-side
    console.log('JupyterLite is ready to launch');
}

function refreshNotebookList() {
    fetch('/notebooks/files')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotebookSidebar(data.files);
            console.log(`Found ${data.files.length} notebook files`);
        }
    })
    .catch(error => {
        console.log('Failed to refresh notebook list:', error);
    });
}

function updateNotebookSidebar(files) {
    const notebooksList = document.getElementById('notebooks-list');
    if (!notebooksList) return;
    
    if (files.length === 0) {
        notebooksList.innerHTML = '<div class="text-muted small">No notebooks created yet</div>';
        return;
    }
    
    let html = '';
    files.forEach(notebook => {
        const displayName = notebook.name.length > 25 ? notebook.name.substring(0, 25) + '...' : notebook.name;
        html += `
            <div class="notebook-item mb-1">
                <a href="#" onclick="openNotebookInJupyterLite('${notebook.name}')" 
                   class="d-block text-decoration-none p-2 rounded small text-white"
                   title="${notebook.name} (${notebook.size_mb}MB, modified ${notebook.modified_str})"
                   style="background: rgba(255,255,255,0.1);">
                    <i class="fa fa-file-code-o me-1 text-white"></i>
                    <span class="notebook-name text-white d-block">${displayName}</span>
                    <small class="text-white-50">${notebook.modified_str}</small>
                </a>
            </div>
        `;
    });
    
    notebooksList.innerHTML = html;
}

// Global function to open notebook files in JupyterLite
window.openNotebookInJupyterLite = function(notebookName) {
    // Launch JupyterLite with the specific notebook
    const iframe = document.getElementById('jupyterlite-container');
    const container = document.getElementById('embedded-container');
    
    // Set iframe source to JupyterLite with the notebook
    iframe.src = `/static/jupyterlite/lab/index.html?path=${encodeURIComponent(notebookName)}`;
    
    // Show embedded container
    container.style.display = 'block';
    container.scrollIntoView({ behavior: 'smooth' });
};

// Auto-refresh notebook list every 30 seconds
setInterval(() => {
    if (window.location.pathname.includes('/notebooks')) {
        refreshNotebookList();
    }
}, 30000);
</script>
{% endblock %}
