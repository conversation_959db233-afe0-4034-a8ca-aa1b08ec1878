{% extends "base/harmattan.html" %}

{% block title %}Jupyter Notebooks - Harmattan{% endblock %}

{% block header %}
<small class="text-muted">Interactive computing</small>
<h1 class="h4 mt-1">Jupyter Notebooks</h1>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fa fa-code me-2"></i>Python Notebook
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted">Start a Jupyter notebook with Python kernel for data analysis and visualization.</p>
                
                <div id="python-status" class="mb-3">
                    <div class="d-flex align-items-center">
                        <div class="status-indicator me-2" id="python-indicator">
                            <i class="fa fa-circle text-secondary"></i>
                        </div>
                        <span id="python-status-text">Not running</span>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="start-python" onclick="startNotebook('python')">
                        <i class="fa fa-play me-1"></i> Start Python Notebook
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="open-python" onclick="openNotebook('python')" style="display: none;">
                        <i class="fa fa-external-link me-1"></i> Open Notebook
                    </button>
                    <button type="button" class="btn btn-outline-danger" id="stop-python" onclick="stopNotebook('python')" style="display: none;">
                        <i class="fa fa-stop me-1"></i> Stop Notebook
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fa fa-bar-chart me-2"></i>R Notebook
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted">Start a Jupyter notebook with R kernel for statistical computing and graphics.</p>
                
                <div id="r-status" class="mb-3">
                    <div class="d-flex align-items-center">
                        <div class="status-indicator me-2" id="r-indicator">
                            <i class="fa fa-circle text-secondary"></i>
                        </div>
                        <span id="r-status-text">Not running</span>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="start-r" onclick="startNotebook('r')">
                        <i class="fa fa-play me-1"></i> Start R Notebook
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="open-r" onclick="openNotebook('r')" style="display: none;">
                        <i class="fa fa-external-link me-1"></i> Open Notebook
                    </button>
                    <button type="button" class="btn btn-outline-danger" id="stop-r" onclick="stopNotebook('r')" style="display: none;">
                        <i class="fa fa-stop me-1"></i> Stop Notebook
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fa fa-info-circle me-2"></i>Getting Started
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Python Notebooks</h6>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-success me-2"></i>Data analysis with pandas</li>
                            <li><i class="fa fa-check text-success me-2"></i>Visualization with matplotlib/seaborn</li>
                            <li><i class="fa fa-check text-success me-2"></i>Machine learning with scikit-learn</li>
                            <li><i class="fa fa-check text-success me-2"></i>Access to DuckDB data</li>
                        </ul>
                        
                        <h6 class="mt-3">Sample Code</h6>
                        <pre class="bg-light p-2 rounded small"><code>import pandas as pd
import duckdb

# Connect to Harmattan database
conn = duckdb.connect('../dbs/harmattan.db')

# Query data
df = conn.execute("SELECT * FROM your_table").df()
print(df.head())</code></pre>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>R Notebooks</h6>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-success me-2"></i>Statistical analysis</li>
                            <li><i class="fa fa-check text-success me-2"></i>Data visualization with ggplot2</li>
                            <li><i class="fa fa-check text-success me-2"></i>Advanced modeling</li>
                            <li><i class="fa fa-check text-success me-2"></i>Access to DuckDB data</li>
                        </ul>
                        
                        <h6 class="mt-3">Sample Code</h6>
                        <pre class="bg-light p-2 rounded small"><code>library(DBI)
library(duckdb)

# Connect to Harmattan database
con <- dbConnect(duckdb(), "../dbs/harmattan.db")

# Query data
df <- dbGetQuery(con, "SELECT * FROM your_table")
head(df)</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">Notebook Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="statusModalContent">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>Processing...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let statusModal;

document.addEventListener('DOMContentLoaded', function() {
    statusModal = new bootstrap.Modal(document.getElementById('statusModal'));
    checkStatus();
    
    // Check status every 10 seconds
    setInterval(checkStatus, 10000);
});

function startNotebook(kernelType) {
    showStatusModal(`Starting ${kernelType.toUpperCase()} Notebook`, 'Starting notebook server...');
    
    fetch(`/notebooks/start/${kernelType}`, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatus(kernelType, true, data.url);
            showStatusModal('Success', data.message + `<br><br><a href="${data.url}" target="_blank" class="btn btn-primary">Open Notebook</a>`);
        } else {
            showStatusModal('Error', data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Network error: ' + error.message);
    });
}

function stopNotebook(kernelType) {
    showStatusModal(`Stopping ${kernelType.toUpperCase()} Notebook`, 'Stopping notebook server...');
    
    fetch(`/notebooks/stop/${kernelType}`, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatus(kernelType, false);
            showStatusModal('Success', data.message);
        } else {
            showStatusModal('Error', data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Network error: ' + error.message);
    });
}

function openNotebook(kernelType) {
    const port = kernelType === 'python' ? 8888 : 8889;
    window.open(`http://localhost:${port}`, '_blank');
}

function checkStatus() {
    fetch('/notebooks/status')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            for (const [kernelType, status] of Object.entries(data.status)) {
                updateStatus(kernelType, status.running, status.url);
            }
        }
    })
    .catch(error => {
        console.error('Error checking status:', error);
    });
}

function updateStatus(kernelType, isRunning, url = null) {
    const indicator = document.getElementById(`${kernelType}-indicator`);
    const statusText = document.getElementById(`${kernelType}-status-text`);
    const startBtn = document.getElementById(`start-${kernelType}`);
    const openBtn = document.getElementById(`open-${kernelType}`);
    const stopBtn = document.getElementById(`stop-${kernelType}`);

    if (isRunning) {
        indicator.innerHTML = '<i class="fa fa-circle text-success"></i>';
        statusText.textContent = 'Running';
        startBtn.style.display = 'none';
        openBtn.style.display = 'block';
        stopBtn.style.display = 'block';
        
        if (url) {
            openBtn.onclick = () => window.open(url, '_blank');
        }
    } else {
        indicator.innerHTML = '<i class="fa fa-circle text-secondary"></i>';
        statusText.textContent = 'Not running';
        startBtn.style.display = 'block';
        openBtn.style.display = 'none';
        stopBtn.style.display = 'none';
    }
}

function showStatusModal(title, content) {
    document.getElementById('statusModalTitle').textContent = title;
    document.getElementById('statusModalContent').innerHTML = content;
    statusModal.show();
}
</script>
{% endblock %}
