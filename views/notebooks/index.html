{% extends "base/harmattan.html" %}

{% block title %}Jupyter Notebooks - Harmattan{% endblock %}

{% block extra_css %}
<style>
.status-indicator {
    display: inline-block;
}

/* Smooth iframe transitions */
#notebook-iframe {
    transition: height 0.3s ease-in-out;
    background: #fff;
}

/* Loading state for iframe */
#notebook-iframe.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Notebook container improvements */
#notebook-container .card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

#notebook-container .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

#notebook-container .card-header .btn {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

#notebook-container .card-header .btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}
</style>
{% endblock %}

{% block header %}
<small class="text-muted">Interactive computing</small>
<h1 class="h4 mt-1">Jupyter Notebooks</h1>
{% endblock %}

{% block header_actions %}
<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createNotebookModal">
    <i class="fa fa-plus me-1"></i> New Notebook
</button>
<button type="button" class="btn btn-outline-warning" onclick="restartAllServers()">
    <i class="fa fa-refresh me-1"></i> Restart Servers
</button>
<button type="button" class="btn btn-outline-info" onclick="refreshNotebookList()">
    <i class="fa fa-refresh me-1"></i> Refresh Files
</button>
{% endblock %}

{% block content %}
<!-- Embedded Jupyter Notebook Container -->
<div id="notebook-container" class="mb-4" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fa fa-file-code-o me-2"></i>
                <span id="notebook-title">Jupyter Notebook</span>
            </h6>
            <div>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFullscreen()">
                    <i class="fa fa-expand" id="fullscreen-icon"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="closeNotebook()">
                    <i class="fa fa-times"></i> Close
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <iframe id="notebook-iframe"
                    src=""
                    style="width: 100%; height: 70vh; border: none; border-radius: 0 0 0.375rem 0.375rem; overflow: hidden;"
                    onload="setupIframeResizing()">
            </iframe>
        </div>
    </div>
</div>

<div class="row" id="server-controls">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fa fa-code me-2"></i>Python Notebook
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted">Start a Jupyter notebook with Python kernel for data analysis and visualization.</p>
                
                <div id="python-status" class="mb-3">
                    <div class="d-flex align-items-center">
                        <div class="status-indicator me-2" id="python-indicator">
                            <i class="fa fa-circle text-secondary"></i>
                        </div>
                        <span id="python-status-text">Not running</span>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="start-python" onclick="startNotebook('python')">
                        <i class="fa fa-play me-1"></i> Start Python Notebook
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="open-python" onclick="openNotebook('python')" style="display: none;">
                        <i class="fa fa-external-link me-1"></i> Open Notebook
                    </button>
                    <button type="button" class="btn btn-outline-danger" id="stop-python" onclick="stopNotebook('python')" style="display: none;">
                        <i class="fa fa-stop me-1"></i> Stop Notebook
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fa fa-bar-chart me-2"></i>R Notebook
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted">Start a Jupyter notebook with R kernel for statistical computing and graphics.</p>
                
                <div id="r-status" class="mb-3">
                    <div class="d-flex align-items-center">
                        <div class="status-indicator me-2" id="r-indicator">
                            <i class="fa fa-circle text-secondary"></i>
                        </div>
                        <span id="r-status-text">Not running</span>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="start-r" onclick="startNotebook('r')">
                        <i class="fa fa-play me-1"></i> Start R Notebook
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="open-r" onclick="openNotebook('r')" style="display: none;">
                        <i class="fa fa-external-link me-1"></i> Open Notebook
                    </button>
                    <button type="button" class="btn btn-outline-danger" id="stop-r" onclick="stopNotebook('r')" style="display: none;">
                        <i class="fa fa-stop me-1"></i> Stop Notebook
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fa fa-info-circle me-2"></i>Getting Started
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Python Notebooks</h6>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-success me-2"></i>Data analysis with pandas</li>
                            <li><i class="fa fa-check text-success me-2"></i>Visualization with matplotlib/seaborn</li>
                            <li><i class="fa fa-check text-success me-2"></i>Machine learning with scikit-learn</li>
                            <li><i class="fa fa-check text-success me-2"></i>Access to DuckDB data</li>
                        </ul>
                        
                        <h6 class="mt-3">Sample Code</h6>
                        <pre class="bg-light p-2 rounded small"><code>import pandas as pd
import duckdb

# Connect to Harmattan database
conn = duckdb.connect('../dbs/harmattan.db')

# Query data
df = conn.execute("SELECT * FROM your_table").df()
print(df.head())</code></pre>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>R Notebooks</h6>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-success me-2"></i>Statistical analysis</li>
                            <li><i class="fa fa-check text-success me-2"></i>Data visualization with ggplot2</li>
                            <li><i class="fa fa-check text-success me-2"></i>Advanced modeling</li>
                            <li><i class="fa fa-check text-success me-2"></i>Access to DuckDB data</li>
                        </ul>
                        
                        <h6 class="mt-3">Sample Code</h6>
                        <pre class="bg-light p-2 rounded small"><code>library(DBI)
library(duckdb)

# Connect to Harmattan database
con <- dbConnect(duckdb(), "../dbs/harmattan.db")

# Query data
df <- dbGetQuery(con, "SELECT * FROM your_table")
head(df)</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Notebook Modal -->
<div class="modal fade" id="createNotebookModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Notebook</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createNotebookForm">
                    <div class="mb-3">
                        <label for="notebookName" class="form-label">Notebook Name</label>
                        <input type="text" class="form-control" id="notebookName"
                               placeholder="Enter notebook name" required>
                        <div class="form-text">The .ipynb extension will be added automatically</div>
                    </div>
                    <div class="mb-3">
                        <label for="kernelType" class="form-label">Kernel Type</label>
                        <select class="form-select" id="kernelType">
                            <option value="python">Python</option>
                            <option value="r">R</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createNewNotebook()">Create Notebook</button>
            </div>
        </div>
    </div>
</div>

<!-- Status Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">Notebook Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="statusModalContent">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>Processing...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let statusModal;

document.addEventListener('DOMContentLoaded', function() {
    statusModal = new bootstrap.Modal(document.getElementById('statusModal'));
    checkStatus();
    
    // Check status every 10 seconds
    setInterval(checkStatus, 10000);
});

function startNotebook(kernelType) {
    showStatusModal(`Starting ${kernelType.toUpperCase()} Notebook`, 'Starting notebook server...');
    
    fetch(`/notebooks/start/${kernelType}`, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatus(kernelType, true, data.url);
            showStatusModal('Success', data.message + `<br><br><a href="${data.url}" target="_blank" class="btn btn-primary">Open Notebook</a>`);
        } else {
            showStatusModal('Error', data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Network error: ' + error.message);
    });
}

function stopNotebook(kernelType) {
    showStatusModal(`Stopping ${kernelType.toUpperCase()} Notebook`, 'Stopping notebook server...');
    
    fetch(`/notebooks/stop/${kernelType}`, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatus(kernelType, false);
            showStatusModal('Success', data.message);
        } else {
            showStatusModal('Error', data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Network error: ' + error.message);
    });
}

function openNotebook(kernelType) {
    const port = kernelType === 'python' ? 8888 : 8889;
    window.open(`http://localhost:${port}`, '_blank');
}

function checkStatus() {
    fetch('/notebooks/status')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            for (const [kernelType, status] of Object.entries(data.status)) {
                updateStatus(kernelType, status.running, status.url);
            }
        }
    })
    .catch(error => {
        console.error('Error checking status:', error);
    });
}

function updateStatus(kernelType, isRunning, url = null) {
    const indicator = document.getElementById(`${kernelType}-indicator`);
    const statusText = document.getElementById(`${kernelType}-status-text`);
    const startBtn = document.getElementById(`start-${kernelType}`);
    const openBtn = document.getElementById(`open-${kernelType}`);
    const stopBtn = document.getElementById(`stop-${kernelType}`);

    if (isRunning) {
        indicator.innerHTML = '<i class="fa fa-circle text-success"></i>';
        statusText.textContent = 'Running';
        startBtn.style.display = 'none';
        openBtn.style.display = 'block';
        stopBtn.style.display = 'block';
        
        if (url) {
            openBtn.onclick = () => window.open(url, '_blank');
        }
    } else {
        indicator.innerHTML = '<i class="fa fa-circle text-secondary"></i>';
        statusText.textContent = 'Not running';
        startBtn.style.display = 'block';
        openBtn.style.display = 'none';
        stopBtn.style.display = 'none';
    }
}

function showStatusModal(title, content) {
    document.getElementById('statusModalTitle').textContent = title;
    document.getElementById('statusModalContent').innerHTML = content;
    statusModal.show();
}

function refreshNotebookList() {
    fetch('/notebooks/files')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotebookSidebar(data.files);
            showStatusModal('Success', `Found ${data.files.length} notebook files`);
        } else {
            showStatusModal('Error', 'Failed to refresh notebook list: ' + data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Network error: ' + error.message);
    });
}

function updateNotebookSidebar(files) {
    const notebooksList = document.getElementById('notebooks-list');
    if (!notebooksList) return;

    if (files.length === 0) {
        notebooksList.innerHTML = '<div class="text-muted small">No notebooks created yet</div>';
        return;
    }

    let html = '';
    files.forEach(notebook => {
        const displayName = notebook.name.length > 25 ? notebook.name.substring(0, 25) + '...' : notebook.name;
        html += `
            <div class="notebook-item mb-1">
                <a href="#" onclick="openNotebookFile('${notebook.path}')"
                   class="d-block text-decoration-none p-2 rounded small text-white"
                   title="${notebook.name} (${notebook.size_mb}MB, modified ${notebook.modified_str})"
                   style="background: rgba(255,255,255,0.1);">
                    <i class="fa fa-file-code-o me-1 text-white"></i>
                    <span class="notebook-name text-white d-block">${displayName}</span>
                    <small class="text-white-50">${notebook.modified_str}</small>
                </a>
            </div>
        `;
    });

    notebooksList.innerHTML = html;
}

// Auto-refresh notebook list every 30 seconds when on notebooks page
setInterval(() => {
    if (window.location.pathname.includes('/notebooks')) {
        fetch('/notebooks/files')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotebookSidebar(data.files);
            }
        })
        .catch(error => {
            console.log('Auto-refresh failed:', error);
        });
    }
}, 30000);

// Global function to open notebook files (called from sidebar)
window.openNotebookFile = function(notebookPath) {
    fetch(`/notebooks/open/${notebookPath}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.servers && data.servers.length > 0) {
                // Show options for embedded or external opening
                let message = 'Open notebook:<br><br>';
                message += '<div class="d-grid gap-2">';

                data.servers.forEach(server => {
                    message += `
                        <button class="btn btn-primary" onclick="openNotebookEmbedded('${server.url}', '${notebookPath}', '${server.kernel}')">
                            <i class="fa fa-window-maximize me-1"></i>Open in Harmattan (${server.kernel.toUpperCase()})
                        </button>
                        <a href="${server.url}" target="_blank" class="btn btn-outline-primary">
                            <i class="fa fa-external-link me-1"></i>Open in New Tab (${server.kernel.toUpperCase()})
                        </a>
                    `;
                });
                message += '</div>';
                showStatusModal('Open Notebook', message);
            } else {
                showStatusModal('Info', data.message);
            }
        } else {
            showStatusModal('Error', data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Failed to open notebook: ' + error.message);
    });
};

// Function to open notebook embedded in Harmattan
function openNotebookEmbedded(serverUrl, notebookPath, kernelType) {
    const notebookContainer = document.getElementById('notebook-container');
    const notebookIframe = document.getElementById('notebook-iframe');
    const notebookTitle = document.getElementById('notebook-title');
    const serverControls = document.getElementById('server-controls');

    // Add loading state
    notebookIframe.classList.add('loading');

    // Set the iframe source
    notebookIframe.src = serverUrl;

    // Update title
    const fileName = notebookPath.split('/').pop();
    notebookTitle.textContent = `${fileName} (${kernelType.toUpperCase()}) - Loading...`;

    // Show notebook container and hide server controls
    notebookContainer.style.display = 'block';
    serverControls.style.display = 'none';

    // Close the modal
    if (statusModal) {
        statusModal.hide();
    }

    // Scroll to notebook
    notebookContainer.scrollIntoView({ behavior: 'smooth' });

    // Remove loading state when iframe loads
    notebookIframe.onload = function() {
        notebookIframe.classList.remove('loading');
        notebookTitle.textContent = `${fileName} (${kernelType.toUpperCase()})`;
        setupIframeResizing();
    };
}

// Function to close embedded notebook
function closeNotebook() {
    const notebookContainer = document.getElementById('notebook-container');
    const notebookIframe = document.getElementById('notebook-iframe');
    const serverControls = document.getElementById('server-controls');

    // Clean up intervals and observers
    if (notebookIframe.resizeInterval) {
        clearInterval(notebookIframe.resizeInterval);
        delete notebookIframe.resizeInterval;
    }

    if (notebookIframe.mutationObserver) {
        notebookIframe.mutationObserver.disconnect();
        delete notebookIframe.mutationObserver;
    }

    // Hide notebook container and show server controls
    notebookContainer.style.display = 'none';
    serverControls.style.display = 'block';

    // Clear iframe source to stop the notebook
    notebookIframe.src = '';

    // Reset iframe height
    notebookIframe.style.height = '70vh';
}

// Function to toggle fullscreen mode
function toggleFullscreen() {
    const notebookIframe = document.getElementById('notebook-iframe');
    const fullscreenIcon = document.getElementById('fullscreen-icon');

    // Check if we're in fullscreen mode by looking at the icon
    const isFullscreen = fullscreenIcon.className.includes('fa-compress');

    if (isFullscreen) {
        // Exit fullscreen - re-enable dynamic resizing
        fullscreenIcon.className = 'fa fa-expand';
        notebookIframe.style.maxHeight = window.innerHeight * 0.9 + 'px';

        // Re-trigger height adjustment
        setTimeout(() => adjustIframeHeight(notebookIframe), 100);
    } else {
        // Enter fullscreen - use fixed height
        notebookIframe.style.height = '95vh';
        notebookIframe.style.maxHeight = '95vh';
        fullscreenIcon.className = 'fa fa-compress';
    }
}

// Add keyboard shortcut for closing notebook (Escape key)
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const notebookContainer = document.getElementById('notebook-container');
        if (notebookContainer.style.display === 'block') {
            closeNotebook();
        }
    }
});

// Function to create new notebook
function createNewNotebook() {
    const notebookName = document.getElementById('notebookName').value.trim();
    const kernelType = document.getElementById('kernelType').value;

    if (!notebookName) {
        alert('Please enter a notebook name');
        return;
    }

    fetch('/notebooks/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: notebookName,
            kernel: kernelType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close the create modal
            const createModal = bootstrap.Modal.getInstance(document.getElementById('createNotebookModal'));
            createModal.hide();

            // Clear the form
            document.getElementById('createNotebookForm').reset();

            // Refresh the notebook list
            refreshNotebookList();

            // Show success message
            showStatusModal('Success', `Notebook "${data.path}" created successfully!`);
        } else {
            showStatusModal('Error', data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Failed to create notebook: ' + error.message);
    });
}

// Function to restart all notebook servers
function restartAllServers() {
    showStatusModal('Restarting Servers', 'Stopping all notebook servers and applying new configuration...');

    fetch('/notebooks/restart_all')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatusModal('Success', `${data.message}<br><br>You can now start servers with iframe embedding support.`);
            // Refresh status
            checkStatus();
        } else {
            showStatusModal('Error', data.error);
        }
    })
    .catch(error => {
        showStatusModal('Error', 'Failed to restart servers: ' + error.message);
    });
}

// Function to setup iframe resizing and content targeting
function setupIframeResizing() {
    const iframe = document.getElementById('notebook-iframe');
    if (!iframe || !iframe.src) return;

    try {
        // Wait for Jupyter to fully load
        setTimeout(() => {
            setupJupyterContentTargeting(iframe);
        }, 3000);

        // Set up periodic height adjustment
        const resizeInterval = setInterval(() => {
            if (iframe.style.display === 'none' || !iframe.src) {
                clearInterval(resizeInterval);
                return;
            }
            adjustIframeHeight(iframe);
        }, 1000);

        // Store interval ID for cleanup
        iframe.resizeInterval = resizeInterval;

    } catch (error) {
        console.log('Iframe resizing setup failed:', error);
    }
}

// Function to target specific Jupyter content and adjust height
function adjustIframeHeight(iframe) {
    try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // Target the main Jupyter content area
        const contentSelectors = [
            'div.jp-WindowedPanel-inner',
            'div.jp-NotebookPanel-notebook',
            'div.jp-Notebook',
            '#main-panel',
            '#notebook-container',
            'body'
        ];

        let targetElement = null;
        for (const selector of contentSelectors) {
            targetElement = iframeDoc.querySelector(selector);
            if (targetElement) break;
        }

        if (targetElement) {
            // Get the actual content height
            const contentHeight = Math.max(
                targetElement.scrollHeight,
                targetElement.offsetHeight,
                targetElement.clientHeight
            );

            // Add some padding and ensure minimum height
            const minHeight = 400;
            const maxHeight = window.innerHeight * 0.9; // 90% of viewport
            const newHeight = Math.min(Math.max(contentHeight + 50, minHeight), maxHeight);

            // Apply the new height
            iframe.style.height = newHeight + 'px';

            // Hide Jupyter's own scrollbars and navigation
            hideJupyterChrome(iframeDoc);
        }

    } catch (error) {
        // Cross-origin restrictions - fallback to default behavior
        console.log('Cannot access iframe content (cross-origin):', error);
    }
}

// Function to hide Jupyter chrome elements for cleaner embedding
function hideJupyterChrome(iframeDoc) {
    try {
        // Inject CSS to hide unnecessary Jupyter UI elements
        let style = iframeDoc.getElementById('harmattan-iframe-styles');
        if (!style) {
            style = iframeDoc.createElement('style');
            style.id = 'harmattan-iframe-styles';
            style.textContent = `
                /* Hide Jupyter header and menu bar */
                #jp-top-panel,
                #jp-menu-panel,
                .jp-MenuBar,
                .jp-TopBar,
                #header,
                #menubar-container,
                #maintoolbar-container {
                    display: none !important;
                }

                /* Hide scrollbars */
                body {
                    overflow: hidden !important;
                }

                /* Adjust main content area */
                #jp-main-dock-panel,
                .jp-WindowedPanel-inner,
                .jp-NotebookPanel-notebook {
                    height: auto !important;
                    overflow: visible !important;
                }

                /* Hide status bar */
                .jp-StatusBar {
                    display: none !important;
                }

                /* Ensure content is visible */
                .jp-Notebook {
                    padding: 10px !important;
                }
            `;
            iframeDoc.head.appendChild(style);
        }
    } catch (error) {
        console.log('Cannot inject styles:', error);
    }
}

// Function to setup content targeting for Jupyter
function setupJupyterContentTargeting(iframe) {
    try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // Wait for Jupyter to be fully loaded
        const checkJupyterReady = setInterval(() => {
            const jupyterApp = iframeDoc.querySelector('.jp-NotebookPanel') ||
                             iframeDoc.querySelector('#notebook-container') ||
                             iframeDoc.querySelector('.jp-WindowedPanel-inner');

            if (jupyterApp) {
                clearInterval(checkJupyterReady);

                // Hide chrome elements
                hideJupyterChrome(iframeDoc);

                // Initial height adjustment
                adjustIframeHeight(iframe);

                // Set up mutation observer for dynamic content changes
                if (window.MutationObserver) {
                    const observer = new MutationObserver(() => {
                        adjustIframeHeight(iframe);
                    });

                    observer.observe(jupyterApp, {
                        childList: true,
                        subtree: true,
                        attributes: true
                    });

                    // Store observer for cleanup
                    iframe.mutationObserver = observer;
                }
            }
        }, 500);

        // Clear check after 30 seconds if Jupyter doesn't load
        setTimeout(() => clearInterval(checkJupyterReady), 30000);

    } catch (error) {
        console.log('Jupyter content targeting failed:', error);
    }
}
</script>
{% endblock %}
