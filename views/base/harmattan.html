<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Harmattan - Unified Analytics Platform">
    <meta name="keyword" content="Harmattan, Analytics, DuckDB, SQL, Jupyter, Data">
    <title>{% block title %}Harmattan - Analytics Platform{% endblock %}</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">

    <!-- project css file  -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/al.style.min.css') }}">
    
    <!-- project layout css file -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout.a.min.css') }}">
    
    {% block extra_css %}{% endblock %}
</head>

<body>

<div id="layout-a" class="theme-blue">

    <!-- Navigation -->
    <div class="navigation navbar navbar-light justify-content-center px-3 px-lg-2 py-2 py-md-3 border-end">

        <!-- Brand -->
        <a href="{{ url_for('dashboard.index') }}" class="mb-0 mb-lg-3 brand-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="30" viewBox="0 0 64 80" fill="none">
                <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
            </svg>
        </a>

        <!-- Menu: icon -->
        <ul class="nav navbar-nav flex-row flex-sm-column flex-grow-1 justify-content-start py-2 py-lg-0">
            <!-- Create group -->
            <li class="nav-item"><a class="nav-link p-2 p-lg-3 d-block d-xl-none menu-toggle me-2 me-lg-0" href="#"><i class="fa fa-bars"></i></a></li>
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="{{ url_for('dashboard.index') }}" title="Dashboard"><i class="fa fa-dashboard"></i></a></li>
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="{{ url_for('sql_client.index') }}" title="SQL Client"><i class="fa fa-code"></i></a></li>
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="{{ url_for('notebooks.index') }}" title="Jupyter Notebooks"><i class="fa fa-book"></i></a></li>
            <li class="nav-item d-none d-sm-block flex-grow-1"></li>

            <!-- Menu collapse -->
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="#" title="Settings"><i class="fa fa-gear"></i></a></li>
            <li class="nav-item d-none d-sm-block"><a class="nav-link p-2 p-lg-3" href="{{ url_for('auth.logout') }}" title="Logout"><i class="fa fa-sign-out"></i></a></li>
        </ul>

    </div>

    <!-- sidebar -->
    <div class="sidebar px-3 py-2 py-md-3">
        <div class="d-flex flex-column h-100">
            <h4 class="sidebar-title mb-4 mt-2">Harmattan</h4>
            
            <!-- Database Tables List -->
            {% if request.endpoint != 'notebooks.index' %}
            <div class="mb-3">
                <h6 class="text-muted mb-2">Database Tables</h6>
                <div id="tables-list" class="tables-list" style="max-height: 300px; overflow-y: auto;">
                    {% if tables %}
                        {% for table in tables %}
                        <div class="table-item mb-1">
                            <a href="{{ url_for('dashboard.view_table', table_name=table.name) }}"
                               class="d-block text-decoration-none p-2 rounded small text-white"
                               title="{{ table.name }} ({{ table.row_count }} rows, {{ table.file_size_mb }}MB)"
                               style="background: rgba(255,255,255,0.1);">
                                <i class="fa fa-table me-1 text-white"></i>
                                <span class="table-name text-white">{{ table.name[:20] }}{% if table.name|length > 20 %}...{% endif %}</span>
                                <small class="text-white-50 d-block">{{ table.row_count }} rows</small>
                            </a>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-muted small">No tables available</div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Notebook Files List (only on notebooks page) -->
            {% if request.endpoint == 'notebooks.index' %}
            <div class="mb-3">
                <h6 class="text-muted mb-2">Notebook Files</h6>
                <div id="notebooks-list" class="notebooks-list" style="max-height: 300px; overflow-y: auto;">
                    {% if notebook_files %}
                        {% for notebook in notebook_files %}
                        <div class="notebook-item mb-1">
                            <a href="#" onclick="openNotebookInJupyterLite('{{ notebook.name }}')"
                               class="d-block text-decoration-none p-2 rounded small text-white"
                               title="{{ notebook.name }} ({{ notebook.size_mb }}MB, modified {{ notebook.modified_str }})"
                               style="background: rgba(255,255,255,0.1);">
                                <i class="fa fa-file-code-o me-1 text-white"></i>
                                <span class="notebook-name text-white d-block">{{ notebook.name[:25] }}{% if notebook.name|length > 25 %}...{% endif %}</span>
                                <small class="text-white-50">{{ notebook.modified_str }}</small>
                            </a>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-muted small">No notebooks created yet</div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Menu: menu collapse btn -->
            <button type="button" class="btn btn-link sidebar-mini-btn text-light">
                <span><i class="fa fa-arrow-left"></i></span>
            </button>
        </div>
    </div>

    <!-- main body area -->
    <div class="main px-xl-5 px-lg-4 px-md-3">

        <!-- Body: Header -->
        <div class="body-header border-bottom d-flex py-3">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col">
                        {% block header %}
                        <small class="text-muted">Welcome to Harmattan</small>
                        <h1 class="h4 mt-1">Analytics Platform</h1>
                        {% endblock %}
                    </div>
                    <div class="col-auto">
                        {% block header_actions %}{% endblock %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex py-lg-4 py-3">
            <div class="container-fluid">
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
                
            </div>
        </div>

    </div>

</div>

<!-- Core JS files -->
<script src="{{ url_for('static', filename='bundles/libscripts.bundle.js') }}"></script>
<script src="{{ url_for('static', filename='js/template.js') }}"></script>

<!-- Global notebook functions -->
<script>
// Global function to open notebook files in JupyterLite (for sidebar)
window.openNotebookInJupyterLite = function(notebookName) {
    // Check if we're on the notebooks page
    if (window.location.pathname.includes('/notebooks')) {
        // If the function exists on the page, use it
        if (typeof window.openNotebookInJupyterLite !== 'undefined') {
            // Launch JupyterLite with the specific notebook
            const iframe = document.getElementById('jupyterlite-container');
            const container = document.getElementById('embedded-container');

            if (iframe && container) {
                // Set iframe source to JupyterLite with the notebook
                iframe.src = `/static/jupyterlite/lab/index.html?path=${encodeURIComponent(notebookName)}`;

                // Show embedded container
                container.style.display = 'block';
                container.scrollIntoView({ behavior: 'smooth' });
            } else {
                // Fallback: open in new tab
                window.open(`/static/jupyterlite/lab/index.html?path=${encodeURIComponent(notebookName)}`, '_blank');
            }
        }
    } else {
        // Not on notebooks page, open in new tab
        window.open(`/static/jupyterlite/lab/index.html?path=${encodeURIComponent(notebookName)}`, '_blank');
    }
};

// Fallback for old function name
window.openNotebookFile = window.openNotebookInJupyterLite;
</script>

{% block extra_js %}{% endblock %}

</body>
</html>
