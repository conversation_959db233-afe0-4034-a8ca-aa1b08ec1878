{% extends "base/harmattan.html" %}

{% block title %}Dashboard - Harmattan{% endblock %}

{% block header %}
<small class="text-muted">Welcome to Harmattan</small>
<h1 class="h4 mt-1">Dashboard</h1>
{% endblock %}

{% block header_actions %}
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
    <i class="fa fa-upload me-1"></i> Upload Data
</button>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row g-2 mb-4 row-deck">
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-database fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">Total Tables</div>
                <div><span class="h4">{{ tables|length }}</span></div>
                <small class="text-muted">Database tables available</small>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-table fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">Total Rows</div>
                <div><span class="h4">{{ tables|sum(attribute='row_count') if tables else 0 }}</span></div>
                <small class="text-muted">Across all tables</small>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-hdd-o fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">Storage Used</div>
                <div><span class="h4">{{ "%.1f"|format(tables|sum(attribute='file_size_mb') if tables else 0) }} MB</span></div>
                <small class="text-muted">Total data storage</small>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-code fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">SQL Client</div>
                <div><a href="{{ url_for('sql_client.index') }}" class="btn btn-sm btn-outline-primary">Open</a></div>
                <small class="text-muted">Query your data</small>
            </div>
        </div>
    </div>
</div>

<!-- Tables List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">Database Tables</h6>
            </div>
            <div class="card-body">
                {% if tables %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Table Name</th>
                                <th>Rows</th>
                                <th>Size</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for table in tables %}
                            <tr>
                                <td>
                                    <i class="fa fa-table me-2"></i>
                                    <strong>{{ table.name }}</strong>
                                </td>
                                <td>{{ "{:,}".format(table.row_count) if table.row_count != 'Error' else 'Error' }}</td>
                                <td>{{ table.file_size_mb }} MB</td>
                                <td>
                                    <a href="{{ url_for('dashboard.view_table', table_name=table.name) }}" 
                                       class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fa fa-eye"></i> View
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete('{{ table.name }}')">
                                        <i class="fa fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fa fa-database fa-3x text-muted mb-3"></i>
                    <h5>No tables found</h5>
                    <p class="text-muted">Upload your first dataset to get started</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fa fa-upload me-1"></i> Upload Data
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Data File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('dashboard.upload_file') }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="table_name" class="form-label">Table Name</label>
                        <input type="text" class="form-control" id="table_name" name="table_name" 
                               placeholder="Enter table name" required>
                        <div class="form-text">Table name will be converted to lowercase</div>
                    </div>
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".csv,.xlsx,.xls,.parquet,.sas7bdat,.rds,.rdata,.rdat" required>
                        <div class="form-text">
                            Supported formats: CSV, Excel, Parquet, SAS, R data files
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the table <strong id="deleteTableName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(tableName) {
    document.getElementById('deleteTableName').textContent = tableName;
    document.getElementById('deleteForm').action = '/delete_table/' + tableName;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
