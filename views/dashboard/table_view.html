{% extends "base/harmattan.html" %}

{% block title %}{{ table_name }} - Harmattan{% endblock %}

{% block header %}
<small class="text-muted">Table Details</small>
<h1 class="h4 mt-1">{{ table_name }}</h1>
{% endblock %}

{% block header_actions %}
<a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
    <i class="fa fa-arrow-left me-1"></i> Back to Dashboard
</a>
{% endblock %}

{% block content %}
{% if table_info %}
<!-- Table Information -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Table Statistics</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="text-muted small">Rows</div>
                        <div class="h5">{{ "{:,}".format(table_info.row_count) }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-muted small">Columns</div>
                        <div class="h5">{{ table_info.columns|length }}</div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="text-muted small">File Size</div>
                    <div>{{ "%.2f"|format(table_info.file_size / (1024 * 1024)) }} MB</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Column Information</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Column Name</th>
                                <th>Data Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for column in table_info.columns %}
                            <tr>
                                <td><code>{{ column.name }}</code></td>
                                <td><span class="badge bg-secondary">{{ column.type }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sample Data -->
{% if sample_data %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Sample Data (First 100 rows)</h6>
                <a href="{{ url_for('sql_client.index') }}?query=SELECT * FROM {{ table_name }}" 
                   class="btn btn-sm btn-primary">
                    <i class="fa fa-code me-1"></i> Query in SQL Client
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                    <table class="table table-sm table-striped mb-0">
                        <thead class="table-dark" style="position: sticky; top: 0; z-index: 10;">
                            <tr>
                                {% for column in sample_data.columns %}
                                <th>{{ column }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in sample_data.data %}
                            <tr>
                                {% for cell in row %}
                                <td>
                                    {% if cell is none %}
                                        <em class="text-muted">NULL</em>
                                    {% else %}
                                        {{ cell }}
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-warning">
    <i class="fa fa-exclamation-triangle me-2"></i>
    Unable to load sample data for this table.
</div>
{% endif %}

{% else %}
<div class="alert alert-danger">
    <i class="fa fa-exclamation-circle me-2"></i>
    Table "{{ table_name }}" not found or could not be accessed.
</div>
{% endif %}
{% endblock %}
