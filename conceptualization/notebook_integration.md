Here are links to documentation for implementing the different ways to build and embed Jupyter notebooks in your app without using iframes:

### JupyterLab Components 🧩
For integrating **JupyterLab components** directly, the main JupyterLab documentation is the best starting point, particularly the sections on developing extensions and reusing UI components.
* [JupyterLab Documentation](https://jupyterlab.readthedocs.io/)

### ThebeLab ⚛️
**ThebeLab** allows you to make static HTML pages interactive by connecting them to a Jupyter kernel.
* [ThebeLab Documentation](https://thebe.readthedocs.io/en/latest/)
* [ThebeLab Configuration Reference](https://thebelab.readthedocs.io/en/latest/config_reference.html)

---

### Converting Notebooks to Web Applications 🚀
These tools allow you to turn Jupyter notebooks into standalone web applications, making them easier to embed or serve independently.

* **Voila**:
    * [Ploomber Docs: Voilà Applications](https://docs.cloud.ploomber.io/en/latest/apps/voila.html) (While getvoila.ai exists, the Ploomber documentation offers more on its application as a web app.)
* **Mercury**:
    * [Mercury Documentation](https://runmercury.com/docs)
    * [Mercury on PyPI](https://pypi.org/project/mercury/1.99.10/) (Provides installation and basic usage information)

---

### Running Jupyter in the Browser (Client-Side) 🌐

* **JupyterLite**: This enables running Jupyter directly in the web browser.
    * [JupyterLite GitHub Documentation](https://github.com/jupyterlite/jupyterlite/blob/main/docs/index.md) (The primary source for JupyterLite documentation)
    * [Project Jupyter | Home](https://jupyter.org/) (Provides an overview and links to try JupyterLite)

---

### API-Level Integration 💻
For advanced integration, you can interact directly with the Jupyter kernel messaging protocol and the JupyterHub API.

* **Jupyter Kernel Messaging Protocol**:
    * [Messaging in Jupyter — jupyter_client documentation](https://jupyter-client.readthedocs.io/en/latest/messaging.html) (This is the authoritative documentation for the protocol.)
* **JupyterHub API**:
    * [JupyterHub Documentation](https://jupyterhub.readthedocs.io/)
    * [JupyterHub API Reference](https://jupyterhub.readthedocs.io/en/4.1.3/reference/api/)

These resources should provide comprehensive guidance for implementing these various embedding strategies.