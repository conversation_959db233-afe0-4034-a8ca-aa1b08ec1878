# Harmattan 
This is a unified analytics platform. It is meant to be similar to Snowflake, Teradata, Palantir and Databricks.
It will be a wrapper for DuckDB and will run most of its processes in the background using the DuckDB python API.
It will have an SQL interface for querying data and an interfacre that supportss creating jupyter notebooks with the languages supprted being python and R.

The app will be a web app built with flask. 
The tree structure will be as shown below:

```
Hamarttan/
|-assets/
|-controllers/
|-models/
|-routes/
|-views/
|-tmp/
|-dbs/
|-app.py
|-requirements.txt
|-README.md
|-LICENSE
```

The static files will be in assets folder and the static file path will be set to assets/ in app.py. The templates folder will be named views and will contain jinja2 templates. The routes will be created as flask blueprints. 


The app will use jinja2 templating for a consistent look and feel.

### Dashboard / Landing route
the main route will feature a list of database tables (these are parquet files in the dbs/ folder).
It will also allow the user to upload new tables to the database. New tables are added by uploading any csv, rds, rdat, parquet, sas7bdat, or excel file. When the files are uploaded if they have a valid structure they will be converted to parquet and added to the database with a name that is specified by the user. For excel files all sheets will be added as separate tables, each prefixed with the name added by the user and then follwed by an underscore and each sheet name (with spaces replaced with underscores). All table names will be converted to all lower case for consistency.




### SQL Client
One of the routes will feature a SQL client interface.
* **Component:** Code Editor (e.g., Monaco, CodeMirror, or a styled `textarea`)
* **Styling:**

  * Full-width, resizable height (default 50% viewport height)
  * **Line numbers enabled**
  * Syntax highlighting for SQL (optional but preferred)

* **User Interactions:**

  * Users can **write SQL** directly into the editor and with syntax highlighting 
  * Users can **highlight a section** of SQL
  * When clicking **Run**, if a selection exists, execute only that part; otherwise, run the full text

**Run Button**

* Positioned at the **top-right** of the editor panel
* Triggers the SQL execution
* Executes either:

  * The **highlighted portion** of the SQL (if any), or
  * The **entire editor contents**


**Result Panel (Bottom Section)**

* Displays the **results of the SQL query** in a clean, scrollable **HTML table**
* Table updates dynamically based on the query results
* Should support:

  * Column headers
  * Scrollable content if many rows

* Clean, **modern look** 
* Minimal distractions – functional, not flashy
* The layout should be **split 50/50 vertically** on large screens but the separator should be **resizable**
* Ensure the result panel is reactive and error-tolerant (e.g., displays query errors if SQL fails)

The SQL will pass on to a python script that will execute it using the DuckDB python API. The results will be returned to the web app and displayed in the result panel.


### Notebooks
Another route will feature the ability to start jupyter notebooks for either python or R.
These will be full featured and will detect the python and R installations on the host computer so the user can select which kernel they want to use.


# UX/Look and feel
The app will have a consistent look and feel. To that extent, apart from the login page, the app will use the ALUI university theme. The pages will extend from the same parent template file that will be named `hamarttan.html`. It will be based specifically on `/alui/university/dist/index.html`. The `div.main > div.body-header > div.container-fluid > div.col > h1.h4` and `div.main > div.body` will be converted to blocks so that they can be used as containers for the content on specific pages in the jinja2 templates.

The `div.sidebar` will contain a list of available database tables instead of a menu, if the table name is too long instead of wraping around it will be truncated with ellipsis, hovering will show the full name. The `div.nav navbar-nav` will remain as is with icons and no text, the icons will be for Dashboard, SQL Client, Jupyter Notebooks. As new functionality is added in future they will show up. The bottom icons for settings and profile will also remain as is. For now we will retain teh ALUI logo till we are ready to replace it.


The login page will be a simple form with a logo and a form for entering the username and password. The logo will be the Harmattan logo. The login page will be at the route /login. The main page will be at the route / and will be the dashboard. The dashboard will have a header with the logo and the name of the app. 


# Additionally
- Use bcrypt for password encryption
- While building use unit tests to ensure we are building what we intend to
- When displaying reults from SQL queries, dont display more than the first 2000 records at any time.
- Allow users download resulting SQL query table in their preferred format (csv, parquet, rds, stata, sas, excel)