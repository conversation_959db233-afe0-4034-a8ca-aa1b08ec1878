<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Hospital Management</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- Plugin css file  -->

    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">
    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.f.min.css">
</head>

<body>

<div id="layout-f" class="theme-cyan">

    <!-- Navigation -->
    <div class="header fixed-top bg-primary">
        <nav class="navbar navbar-light py-2 py-md-3 px-lg-5 px-md-2">
            <div class="container-fluid">

                <!-- Brand -->
                <a href="index.html" class="me-3 me-lg-4 brand-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                        <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
                    </svg>
                </a>

                <!-- Search -->
                <div class="h-left d-none d-sm-block">
                    <div class="input-group border rounded header-search">
                        <button class="btn btn-outline-secondary text-light dropdown-toggle border-0 d-none d-sm-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">Fillter</button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Action</a></li>
                            <li><a class="dropdown-item" href="#">Another action</a></li>
                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Separated link</a></li>
                        </ul>
                        <input type="text" class="form-control bg-transparent border-0" placeholder="Search here...">
                    </div>
                </div>

                <!-- header rightbar icon -->
                <div class="h-right flex-grow-1 justify-content-end d-flex align-items-center me-5 me-lg-0">
                    <div class="d-flex">
                        <a class="nav-link text-secondary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                        <a class="nav-link text-secondary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                            <i class="fa fa-sliders"></i>
                        </a>
                    </div>
                    <div class="dropdown notifications">
                        <a class="nav-link dropdown-toggle pulse text-secondary" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-bell"></i>
                            <span class="pulse-ring"></span>
                        </a>
                        <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w380">
                                <div class="card-header border-0 p-3">
                                    <h5 class="mb-0 fw-light d-flex justify-content-between">
                                        <span>Notifications Center</span>
                                        <span class="badge text-muted">14</span>
                                    </h5>
                                    <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tab-content card-body custom_scroll">
                                    <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Chris Morise</span> <small>2MIN</small></p>
                                                        <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Kelly</span> <small>1DAY</small></p>
                                                        <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">making it over 2000 years old</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">savera</span> <small>1DAY</small></p>
                                                        <span class="text-muted">The generated Lorem Ipsum</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                        <small class="text-muted">10:00 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                        <small class="text-muted">04:00 PM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                        <h4>No Logs right now!</h4>
                                    </div>
                                </div>
                                <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown user-profile ms-2 ms-sm-3">
                        <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                            <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                        </a>
                        <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w240">
                                <div class="card-body border-bottom">
                                    <div class="d-flex py-1">
                                        <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted"><span class="fw-bold">Chris Morise</span></p>
                                            <small class="text-muted"><EMAIL></small>
                                            <div>
                                                <a href="#" class="card-link">Sign out</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group m-2">
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile & account</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </nav>

        <div class="sub-header">
            <nav class="navbar navbar-expand-lg py-0 px-lg-5 px-md-2">
                <div class="container-fluid">

                    <!-- menu toggler -->
                    <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mainHeader">
                        <span class="fa fa-bars"></span>
                    </button>

                    <!-- main menu -->
                    <div class="collapse navbar-collapse order-0 py-1 py-md-2" id="mainHeader">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-dashboard me-2"></i><span>Dashboard</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="index.html">Hospital</a></li>
                                    <li><a class="dropdown-item" href="index-covid.html">Covid-19</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-slack me-2"></i><span>Apps</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="calendar.html">Calendar</a></li>
                                    <li><a class="dropdown-item" href="chat.html">Chat app</a></li>
                                    <li><a class="dropdown-item" href="#">Inbox</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-user-md me-2"></i><span>Doctor</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="doctor-all.html">All Doctors</a></li>
                                    <li><a class="dropdown-item" href="doctor-add.html">Add new doctor</a></li>
                                    <li><a class="dropdown-item" href="doctor-profile.html">Doctors Profile</a></li>
                                    <li><a class="dropdown-item" href="doctor-schedule.html">Doctor Schedule</a></li>
                                    <li><a class="dropdown-item" href="doctor-appointment.html">Book Appointment</a></li>
                                    <li><a class="dropdown-item" href="department.html">Department</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-user-circle-o me-2"></i><span>Patient</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="patient-list.html">Patient List</a></li>
                                    <li><a class="dropdown-item" href="patient-add.html">Add new patient</a></li>
                                    <li><a class="dropdown-item" href="patient-profile.html">Patient Profile</a></li>
                                    <li><a class="dropdown-item" href="patient-invoices.html">Patient Invoices</a></li>
                                    <li><a class="dropdown-item" href="room-allotment.html">Room Allotment</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-credit-card me-2"></i><span>Payments</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="payments-list.html">Payments List</a></li>
                                    <li><a class="dropdown-item" href="payments-add.html">Add Payments</a></li>
                                    <li><a class="dropdown-item" href="payments-invoice.html">Single Invoice</a></li>
                                </ul>
                            </li>
    
                            <li class="nav-item dropdown">
                                <a class="nav-link active dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file me-2"></i><span>Pages</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="auth-signin.html">Sign in</a></li>
                                    <li><a class="dropdown-item" href="auth-signup.html">Sign up</a></li>
                                    <li><a class="dropdown-item" href="auth-password-reset.html">Password reset</a></li>
                                    <li><a class="dropdown-item" href="auth-two-step.html">2-Step Authentication</a></li>
                                    <li><a class="dropdown-item" href="auth-404.html">404</a></li>
                                    <li><a class="dropdown-item" href="profile.html">Profile</a></li>
                                    <li><a class="dropdown-item" href="timeline.html">Timeline</a></li>
                                    <li><a class="dropdown-item" href="imagegallery.html">Image Gallery</a></li>
                                    <li><a class="dropdown-item active" href="invoices.html">Invoices</a></li>
                                    <li><a class="dropdown-item" href="pricing.html">Pricing</a></li>
                                    <li><a class="dropdown-item" href="teamsboard.html">Teams Board</a></li>
                                    <li><a class="dropdown-item" href="faqs.html">FAQs</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/w-cards.html">Widget's</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file-text me-2"></i><span>Docs</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="../../../documentation/stater-page.html">Stater page</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/index.html">Documentation</a></li>
                                    <li><a class="dropdown-item" href="../changelog.html">Changelog</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                </div>
            </nav>
        </div>

    </div>

    <!-- main body area -->
    <div class="main px-lg-5 px-md-2">

        <!-- Body: Header -->
        <div class="body-header d-flex py-lg-3 py-2 text-light">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col">
                        <ol class="breadcrumb d-inline-flex bg-transparent p-0 m-0">
                            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                            <li class="breadcrumb-item">Pages</li>
                            <li class="breadcrumb-item active">invoices</li>
                        </ol>
                        <h1 class="h4 mt-1">invoices Design</h1>
                    </div>
                    <div class="col-auto">
                        <ul class="nav nav-tabs tab-body-header rounded" role="tablist">
                            <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#Invoice-list" role="tab">Invoice List</a></li>
                            <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#Invoice-Simple" role="tab">Simple invoice</a></li>
                            <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#Invoice-Email" role="tab">Email invoice</a></li>
                        </ul>
                    </div>
                </div> <!-- Row end  -->

            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex py-lg-3 py-2">
            <div class="container-fluid">
                <div class="row justify-content-center">

                    <div class="col-lg-12 col-md-12">
                        <div class="tab-content">
                            <div class="tab-pane fade show active" id="Invoice-list">
                                <div class="row justify-content-center">
                                    <div class="col-lg-8 col-md-12">
                                        <div class="card mb-3">
                                            <div class="card-body d-sm-flex justify-content-between">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="d-flex justify-content-between mb-0"><span>Maryam Amiri</span></h6>
                                                        <span class="text-muted">Front-end developer</span>
                                                    </div>
                                                </a>
                                                <div class="text-end d-none d-md-block">
                                                    <p class="mb-1"><i class="fa fa-map-marker ps-1"></i> 123 6th St. Melbourne, FL 32904</p>
                                                    <span class="text-muted"><i class="fa fa-money ps-1"></i> $60 per hour</span>
                                                </div>
                                            </div>
                                            <div class="card-footer justify-content-between d-flex align-items-center">
                                                <div class="d-none d-md-block">
                                                    <strong>Applied on:</strong>
                                                    <span>21 Jan, 2019</span>
                                                </div>
                                                <div class="card-hover-show">
                                                    <a class="btn btn-sm btn-white border lift" href="#">Download</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Send</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-3">
                                            <div class="card-body d-sm-flex justify-content-between">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="d-flex justify-content-between mb-0"><span>Brian Swader</span></h6>
                                                        <span class="text-muted">Front-end developer</span>
                                                    </div>
                                                </a>
                                                <div class="text-end d-none d-md-block">
                                                    <p class="mb-1"><i class="fa fa-map-marker ps-1"></i> 70 Bowman St. South Windsor, CT 06074</p>
                                                    <span class="text-muted"><i class="fa fa-money ps-1"></i> $45 per hour</span>
                                                </div>
                                            </div>
                                            <div class="card-footer justify-content-between d-flex align-items-center">
                                                <div class="d-none d-md-block">
                                                    <strong>Applied on:</strong>
                                                    <span>21 Feb, 2019</span>
                                                </div>
                                                <div class="card-hover-show">
                                                    <a class="btn btn-sm btn-white border lift" href="#">Download</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Send</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-3">
                                            <div class="card-body d-sm-flex justify-content-between">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="d-flex justify-content-between mb-0"><span>Maryam Amiri</span></h6>
                                                        <span class="text-muted">Front-end developer</span>
                                                    </div>
                                                </a>
                                                <div class="text-end d-none d-md-block">
                                                    <p class="mb-1"><i class="fa fa-map-marker ps-1"></i> 44 Shirley Ave. West Chicago, IL 60185</p>
                                                    <span class="text-muted"><i class="fa fa-money ps-1"></i> $25 per hour</span>
                                                </div>
                                            </div>
                                            <div class="card-footer justify-content-between d-flex align-items-center">
                                                <div class="d-none d-md-block">
                                                    <strong>Applied on:</strong>
                                                    <span>21 Jun, 2019</span>
                                                </div>
                                                <div class="card-hover-show">
                                                    <a class="btn btn-sm btn-white border lift" href="#">Download</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Send</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-3">
                                            <div class="card-body d-sm-flex justify-content-between">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="d-flex justify-content-between mb-0"><span>Orlando Lentz</span></h6>
                                                        <span class="text-muted">Front-end developer</span>
                                                    </div>
                                                </a>
                                                <div class="text-end d-none d-md-block">
                                                    <p class="mb-1"><i class="fa fa-map-marker ps-1"></i> 123 6th St. Melbourne, FL 32904</p>
                                                    <span class="text-muted"><i class="fa fa-money ps-1"></i> $25 per hour</span>
                                                </div>
                                            </div>
                                            <div class="card-footer justify-content-between d-flex align-items-center">
                                                <div class="d-none d-md-block">
                                                    <strong>Applied on:</strong>
                                                    <span>21 Jun, 2020</span>
                                                </div>
                                                <div class="card-hover-show">
                                                    <a class="btn btn-sm btn-white border lift" href="#">Download</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Send</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-3">
                                            <div class="card-body d-sm-flex justify-content-between">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="d-flex justify-content-between mb-0"><span>Dr. Hossein Shams</span></h6>
                                                        <span class="text-muted">Front-end developer</span>
                                                    </div>
                                                </a>
                                                <div class="text-end d-none d-md-block">
                                                    <p class="mb-1"><i class="fa fa-map-marker ps-1"></i> 123 6th St. Melbourne, FL 32904</p>
                                                    <span class="text-muted"><i class="fa fa-money ps-1"></i> $25 per hour</span>
                                                </div>
                                            </div>
                                            <div class="card-footer justify-content-between d-flex align-items-center">
                                                <div class="d-none d-md-block">
                                                    <strong>Applied on:</strong>
                                                    <span>21 Jun, 2020</span>
                                                </div>
                                                <div class="card-hover-show">
                                                    <a class="btn btn-sm btn-white border lift" href="#">Download</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Send</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-3">
                                            <div class="card-body d-sm-flex justify-content-between">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="d-flex justify-content-between mb-0"><span>Brian Swader</span></h6>
                                                        <span class="text-muted">Front-end developer</span>
                                                    </div>
                                                </a>
                                                <div class="text-end d-none d-md-block">
                                                    <p class="mb-1"><i class="fa fa-map-marker ps-1"></i> 70 Bowman St. South Windsor, CT 06074</p>
                                                    <span class="text-muted"><i class="fa fa-money ps-1"></i> $45 per hour</span>
                                                </div>
                                            </div>
                                            <div class="card-footer justify-content-between d-flex align-items-center">
                                                <div class="d-none d-md-block">
                                                    <strong>Applied on:</strong>
                                                    <span>21 Feb, 2019</span>
                                                </div>
                                                <div class="card-hover-show">
                                                    <a class="btn btn-sm btn-white border lift" href="#">Download</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Send</a>
                                                    <a class="btn btn-sm btn-white border lift" href="#">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                        <nav aria-label="Page navigation">
                                            <ul class="pagination mt-4">
                                                <li class="page-item"><a class="page-link" href="#">Previous</a></li>
                                                <li class="page-item"><a class="page-link" href="#">1</a></li>
                                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                                <li class="page-item"><a class="page-link" href="#">Next</a></li>
                                            </ul>
                                        </nav>
                                    </div>
                                </div>  <!-- Row end  -->
                            </div> <!-- tab end  -->
                            <div class="tab-pane fade" id="Invoice-Simple">
                                <div class="row justify-content-center">
                                    <div class="col-lg-8 col-md-12">
                                        <div class="card p-xl-5 p-lg-4 p-0">
                                            <div class="card-body">
                                                <div class="mb-3 pb-3 border-bottom">
                                                    Invoice
                                                    <strong>01/Nov/2020</strong>
                                                    <span class="float-right"> <strong>Status:</strong> Pending</span>
                                                </div>

                                                <div class="row mb-4">
                                                    <div class="col-sm-6">
                                                        <h6 class="mb-3">From:</h6>
                                                        <div><strong>Webz Poland</strong></div>
                                                        <div>Madalinskiego 8</div>
                                                        <div>71-101 Szczecin, Poland</div>
                                                        <div>Email: <EMAIL></div>
                                                        <div>Phone: +48 ************</div>
                                                    </div>
                                                    
                                                    <div class="col-sm-6">
                                                        <h6 class="mb-3">To:</h6>
                                                        <div><strong>Bob Mart</strong></div>
                                                        <div>Attn: Daniel Marek</div>
                                                        <div>43-190 Mikolow, Poland</div>
                                                        <div>Email: <EMAIL></div>
                                                        <div>Phone: +48 123 456 789</div>
                                                    </div>
                                                </div> <!-- Row end  -->
                                                
                                                <div class="table-responsive-sm">
                                                    <table class="table table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th class="text-center">#</th>
                                                                <th>Item</th>
                                                                <th>Description</th>
                                                                <th class="text-end">Unit Cost</th>
                                                                <th class="text-center">Qty</th>
                                                                <th class="text-end">Total</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td class="text-center">1</td>
                                                                <td>Origin License</td>
                                                                <td>Extended License</td>
                                                                <td class="text-end">$999,00</td>
                                                                <td class="text-center">1</td>
                                                                <td class="text-end">$999,00</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-center">2</td>
                                                                <td>Custom Services</td>
                                                                <td>Instalation and Customization (cost per hour)</td>
                                                                <td class="text-end">$150,00</td>
                                                                <td class="text-center">20</td>
                                                                <td class="text-end">$3.000,00</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-center">3</td>
                                                                <td>Hosting</td>
                                                                <td>1 year subcription</td>
                                                                <td class="text-end">$499,00</td>
                                                                <td class="text-center">1</td>
                                                                <td class="text-end">$499,00</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-center">4</td>
                                                                <td>Platinum Support</td>
                                                                <td>1 year subcription 24/7</td>
                                                                <td class="text-end">$3.999,00</td>
                                                                <td class="text-center">1</td>
                                                                <td class="text-end">$3.999,00</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                
                                                <div class="row">
                                                    <div class="col-lg-4 col-sm-5">
                                                    
                                                    </div>
                                                    
                                                    <div class="col-lg-4 col-sm-5 ms-auto">
                                                        <table class="table table-clear">
                                                            <tbody>
                                                                <tr>
                                                                    <td ><strong>Subtotal</strong></td>
                                                                    <td class="text-end">$8.497,00</td>
                                                                </tr>
                                                                <tr>
                                                                    <td ><strong>VAT (10%)</strong></td>
                                                                    <td class="text-end">$679,76</td>
                                                                </tr>
                                                                <tr>
                                                                    <td ><strong>Total</strong></td>
                                                                    <td class="text-end"><strong>$7.477,36</strong></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div> <!-- Row end  -->
                
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <h6>Terms &amp; Condition</h6>
                                                        <p class="text-muted">You know, being a test pilot isn't always the healthiest business
                                                        in the world. We predict too much for the next year and yet far
                                                        too little for the next 10.</p>
                                                    </div>
                                                    <div class="col-lg-12 text-end">
                                                        <button type="button" class="btn btn-outline-secondary btn-lg my-1"><i class="fa fa-print"></i> Print</button>
                                                        <button type="button" class="btn btn-info btn-lg my-1"><i class="fa fa-paper-plane-o"></i> Send Invoice</button>
                                                    </div>
                                                </div> <!-- Row end  -->
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- Row end  -->
                            </div> <!-- tab end  -->
                            <div class="tab-pane fade" id="Invoice-Email">
                                <div class="row justify-content-center">
                                    <div class="col-lg-8 col-md-12">
                                        <div class="d-flex justify-content-center">
                                            <table class="card p-5">
                                                <tr>
                                                    <td></td>
                                                    <td style="text-align: center;">
                                                        <table width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td style="text-align: center;">
                                                                    <h2>$33.98 Paid</h2>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding-bottom: 30px 0; padding-top: 10px; text-align: center;">
                                                                    <h4>Thanks for using TTM Inc.</h4>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding: 30px 0;">
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td>
                                                                                Attn: <strong>Daniel Marek</strong> 43-190 Mikolow, Poland<br>
                                                                                Email: <EMAIL><br>
                                                                                Phone: +48 123 456 789<br>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td style="padding-top: 40px;">
                                                                                <table class="table table-bordered" cellpadding="0" cellspacing="0">
                                                                                    <tr>
                                                                                        <td style="text-align: left;">Extended License</td>
                                                                                        <td style="text-align: right;">$19.99</td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td style="text-align: left;">1 year subcription</td>
                                                                                        <td style="text-align: right;">$9.99</td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td style="text-align: left;">Instalation and Customization</td>
                                                                                        <td style="text-align: right;">$4.00</td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td style="text-align: right;" width="80%"><strong>Total</strong></td>
                                                                                        <td style="text-align: right;">$33.98</td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding: 10px 0; text-align: center;">
                                                                    <a href="#">View in browser</a>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding: 0; text-align: center;">
                                                                    TTM Inc. 70 Bowman St. South Windsor, CT 06074
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="100%" style="text-align: center; margin-top: 50px;">
                                                            <tr>
                                                                <td class="aligncenter content-block">Questions? Email <a href="mailto:"><EMAIL></a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                    <td></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div> <!-- Row end  -->
                            </div> <!-- tab end  -->
                        </div>
                    </div>

                </div> <!-- Row end  -->
            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer d-flex">

        </div>

    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title">Ready to build Layouts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-c/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-e/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-k/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-k.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Body Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-p/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-p.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Background BG</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-m/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-m.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Header <span class="small">(Fixed)</span></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">AL-UI Setting</h5>
                    </div>
                    <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan" class="active"><div class="cyan"></div></li>
                            <li data-theme="green"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-1">
                                <input class="form-check-input" type="checkbox" id="CheckGradient">
                                <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                            </div>
                    </div>
                    <!-- Settings: bg image -->
                    <div class="setting-img mb-3">
                        <div class="form-check form-switch imagebg-switch mb-1">
                            <input class="form-check-input" type="checkbox" id="CheckImage">
                            <label class="form-check-label" for="CheckImage">Set Background Image (Sidebar)</label>
                        </div>
                        <div class="bg-images">
                            <ul class="list-unstyled d-flex justify-content-between">
                                <li class="sidebar-img-1 sidebar-img-active"><a class="rounded sidebar-img" id="img-1" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-1.jpg" alt="" /></a></li>
                                <li class="sidebar-img-2"><a class="rounded sidebar-img" id="img-2" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-2.jpg" alt="" /></a></li>
                                <li class="sidebar-img-3"><a class="rounded sidebar-img" id="img-3" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-3.jpg" alt="" /></a></li>
                                <li class="sidebar-img-4"><a class="rounded sidebar-img" id="img-4" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-4.jpg" alt="" /></a></li>
                                <li class="sidebar-img-5"><a class="rounded sidebar-img" id="img-5" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-5.jpg" alt="" /></a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>
<!-- Plugin Js -->

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
</body>
</html>