<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Hospital Management</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">
    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.f.min.css">
    <!-- Prism css file please do not add in your project -->
    <link rel="stylesheet" href="../../../assets/plugin/prismjs/prism.css">
</head>

<body>

<div id="layout-f" class="theme-cyan">

    <!-- Navigation -->
    <div class="header fixed-top bg-primary">
        <nav class="navbar navbar-light py-2 py-md-3 px-lg-5 px-md-2">
            <div class="container-fluid">

                <!-- Brand -->
                <a href="index.html" class="me-3 me-lg-4 brand-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                        <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
                    </svg>
                </a>

                <!-- Search -->
                <div class="h-left d-none d-sm-block">
                    <div class="input-group border rounded header-search">
                        <button class="btn btn-outline-secondary text-light dropdown-toggle border-0 d-none d-sm-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">Fillter</button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Action</a></li>
                            <li><a class="dropdown-item" href="#">Another action</a></li>
                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Separated link</a></li>
                        </ul>
                        <input type="text" class="form-control bg-transparent border-0" placeholder="Search here...">
                    </div>
                </div>

                <!-- header rightbar icon -->
                <div class="h-right flex-grow-1 justify-content-end d-flex align-items-center me-5 me-lg-0">
                    <div class="d-flex">
                        <a class="nav-link text-secondary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                        <a class="nav-link text-secondary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                            <i class="fa fa-sliders"></i>
                        </a>
                    </div>
                    <div class="dropdown notifications">
                        <a class="nav-link dropdown-toggle pulse text-secondary" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-bell"></i>
                            <span class="pulse-ring"></span>
                        </a>
                        <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w380">
                                <div class="card-header border-0 p-3">
                                    <h5 class="mb-0 fw-light d-flex justify-content-between">
                                        <span>Notifications Center</span>
                                        <span class="badge text-muted">14</span>
                                    </h5>
                                    <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tab-content card-body custom_scroll">
                                    <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Chris Morise</span> <small>2MIN</small></p>
                                                        <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Kelly</span> <small>1DAY</small></p>
                                                        <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">making it over 2000 years old</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">savera</span> <small>1DAY</small></p>
                                                        <span class="text-muted">The generated Lorem Ipsum</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                        <small class="text-muted">10:00 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                        <small class="text-muted">04:00 PM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                        <h4>No Logs right now!</h4>
                                    </div>
                                </div>
                                <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown user-profile ms-2 ms-sm-3">
                        <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                            <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                        </a>
                        <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w240">
                                <div class="card-body border-bottom">
                                    <div class="d-flex py-1">
                                        <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted"><span class="fw-bold">Chris Morise</span></p>
                                            <small class="text-muted"><EMAIL></small>
                                            <div>
                                                <a href="#" class="card-link">Sign out</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group m-2">
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile & account</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </nav>

        <!-- Sub header: menu -->
        <div class="sub-header">
            <nav class="navbar navbar-expand-lg py-0 px-lg-5 px-md-2">
                <div class="container-fluid">

                    <!-- menu toggler -->
                    <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mainHeader">
                        <span class="fa fa-bars"></span>
                    </button>

                    <!-- main menu -->
                    <div class="collapse navbar-collapse order-0 py-1 py-md-2" id="mainHeader">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="index.html"><i class="fa fa-dashboard me-2"></i><span>Dashboard</span></a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-slack me-2"></i><span>Apps</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="calendar.html">Calendar</a></li>
                                    <li><a class="dropdown-item" href="chat.html">Chat app</a></li>
                                    <li><a class="dropdown-item" href="#">Inbox</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file me-2"></i><span>Pages</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="profile.html">Profile</a></li>
                                    <li><a class="dropdown-item" href="timeline.html">Timeline</a></li>
                                    <li><a class="dropdown-item" href="imagegallery.html">Image Gallery</a></li>
                                    <li><a class="dropdown-item" href="invoices.html">Invoices</a></li>
                                    <li><a class="dropdown-item" href="pricing.html">Pricing</a></li>
                                    <li><a class="dropdown-item" href="teamsboard.html">Teams Board</a></li>
                                    <li><a class="dropdown-item" href="faqs.html">FAQs</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/w-cards.html">Widget's</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-lock me-2"></i><span>Authentication</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="auth-signin.html">Sign in</a></li>
                                    <li><a class="dropdown-item" href="auth-signup.html">Sign up</a></li>
                                    <li><a class="dropdown-item" href="auth-password-reset.html">Password reset</a></li>
                                    <li><a class="dropdown-item" href="auth-two-step.html">2-Step Authentication</a></li>
                                    <li><a class="dropdown-item" href="auth-404.html">404</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-diamond me-2"></i><span>UI Components</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="ui-alerts.html">Alerts</a></li>
                                    <li><a class="dropdown-item" href="ui-badge.html">Badge</a></li>
                                    <li><a class="dropdown-item" href="ui-breadcrumb.html">Breadcrumb</a></li>
                                    <li><a class="dropdown-item" href="ui-buttons.html">Buttons</a></li>
                                    <li><a class="dropdown-item" href="ui-card.html">Card</a></li>
                                    <li><a class="dropdown-item active" href="ui-carousel.html">Carousel</a></li>
                                    <li><a class="dropdown-item" href="ui-collapse.html">Collapse</a></li>
                                    <li><a class="dropdown-item" href="ui-dropdowns.html">Dropdowns</a></li>
                                    <li><a class="dropdown-item" href="ui-listgroup.html">List group</a></li>
                                    <li><a class="dropdown-item" href="ui-modal.html">Modal</a></li>
                                    <li><a class="dropdown-item" href="ui-navs.html">Navs</a></li>
                                    <li><a class="dropdown-item" href="ui-navbar.html">Navbar</a></li>
                                    <li><a class="dropdown-item" href="ui-pagination.html">Pagination</a></li>
                                    <li><a class="dropdown-item" href="ui-popovers.html">Popovers</a></li>
                                    <li><a class="dropdown-item" href="ui-progress.html">Progress</a></li>
                                    <li><a class="dropdown-item" href="ui-scrollspy.html">Scrollspy</a></li>
                                    <li><a class="dropdown-item" href="ui-spinners.html">Spinners</a></li>
                                    <li><a class="dropdown-item" href="ui-toasts.html">Toasts</a></li>
                                    <li><a class="dropdown-item" href="ui-tooltips.html">Tooltips</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file-text me-2"></i><span>Docs</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="../../../documentation/stater-page.html">Stater page</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/index.html">Documentation</a></li>
                                    <li><a class="dropdown-item" href="../changelog.html">Changelog</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                </div>
            </nav>
        </div>
    </div>

    <!-- main body area -->
    <div class="main px-lg-5 px-md-2">

        <!-- Body: Header -->
        <div class="body-header d-flex py-lg-3 py-2 text-light">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col">
                        <ol class="breadcrumb d-inline-flex bg-transparent p-0 m-0">
                            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                            <li class="breadcrumb-item">Components</li>
                            <li class="breadcrumb-item active">Carousel</li>
                        </ol>
                        <h1 class="h4 mt-1">Carousel</h1>
                    </div>
                    <div class="col-auto">
                        <a href="https://v5.getbootstrap.com/" class="btn btn-dark lift">GetBootstrap</a>
                    </div>
                </div>  <!-- Row end  -->
            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex mb-4">
            <div class="container-fluid">
                <div class="col-12">
                    <div class="card p-4 bd-content">
                        <div class="row justify-content-between">
                            <div class="col-lg-8 col-sm-12">
                                <h2 id="how-it-works">How it works</h2>
                                <p>The carousel is a slideshow for cycling through a series of content, built with CSS 3D transforms and a bit of JavaScript. It works with a series of images, text, or custom markup. It also includes support for previous/next controls and indicators.</p>
                                <p>In browsers where the <a href="https://www.w3.org/TR/page-visibility/">Page Visibility API</a> is supported, the carousel will avoid sliding when the webpage is not visible to the user (such as when the browser tab is inactive, the browser window is minimized, etc.).</p>
                                <div class="card card-callout mb-3">
                                    <div class="card-body">
                                        The animation effect of this component is dependent on the <code>prefers-reduced-motion</code> media query. See the <a href="https://v5.getbootstrap.com/docs/5.0/getting-started/accessibility/#reduced-motion">reduced motion section of our accessibility documentation</a>.
                                    </div>
                                </div>
                                
                                <p>Please be aware that nested carousels are not supported, and carousels are generally not compliant with accessibility standards.</p>
                                <h2 id="example">Example</h2>
                                <p>Carousels don’t automatically normalize slide dimensions. As such, you may need to use additional utilities or custom styles to appropriately size content. While carousels support previous/next controls and indicators, they’re not explicitly required. Add and customize as you see fit.</p>
                                <p><strong>The <code>.active</code> class needs to be added to one of the slides</strong> otherwise the carousel will not be visible. Also be sure to set a unique id on the <code>.carousel</code> for optional controls, especially if you’re using multiple carousels on a single page. Control and indicator elements must have a <code>data-target</code> attribute (or <code>href</code> for links) that matches the id of the <code>.carousel</code> element.</p>
                                
                                <h3 id="slides-only">Slides only</h3>
                                <p>Here’s a carousel with slides only. Note the presence of the <code>.d-block</code> and <code>.w-100</code> on carousel images to prevent browser default image alignment.</p>
                                <div class="bd-example mb-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="carouselExampleSlidesOnly" class="carousel slide" data-ride="carousel">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/10.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/8.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/1.jpg" alt="" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
    <pre>
    <code class="language-html" data-lang="html">&lt;div id=&quot;carouselExampleSlidesOnly&quot; class=&quot;carousel slide&quot; data-ride=&quot;carousel&quot;&gt;
        &lt;div class=&quot;carousel-inner&quot;&gt;
            &lt;div class=&quot;carousel-item active&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/10.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/8.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/1.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;</code>
    </pre>
                                </div>
                                
                                <h3 id="with-controls">With controls</h3>
                                <p>Adding in the previous and next controls:</p>
                                <div class="bd-example mb-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="carouselExampleControls" class="carousel slide" data-ride="carousel">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/10.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/8.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/1.jpg" alt="" />
                                                    </div>
                                                </div>
                                                <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </a>
                                                <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
    <pre>
    <code class="language-html" data-lang="html">&lt;div id=&quot;carouselExampleControls&quot; class=&quot;carousel slide&quot; data-ride=&quot;carousel&quot;&gt;
        &lt;div class=&quot;carousel-inner&quot;&gt;
            &lt;div class=&quot;carousel-item active&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/10.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/8.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/1.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;a class=&quot;carousel-control-prev&quot; href=&quot;#carouselExampleControls&quot; role=&quot;button&quot; data-slide=&quot;prev&quot;&gt;
            &lt;span class=&quot;carousel-control-prev-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Previous&lt;/span&gt;
        &lt;/a&gt;
        &lt;a class=&quot;carousel-control-next&quot; href=&quot;#carouselExampleControls&quot; role=&quot;button&quot; data-slide=&quot;next&quot;&gt;
            &lt;span class=&quot;carousel-control-next-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Next&lt;/span&gt;
        &lt;/a&gt;
    &lt;/div&gt;</code>
    </pre>
                                </div>
                                
                                <h3 id="with-indicators">With indicators</h3>
                                <p>You can also add the indicators to the carousel, alongside the controls, too.</p>
                                <div class="bd-example mb-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
                                                <ol class="carousel-indicators">
                                                    <li data-bs-target="#carouselExampleIndicators" data-slide-to="0" class="active"></li>
                                                    <li data-bs-target="#carouselExampleIndicators" data-slide-to="1" class=""></li>
                                                    <li data-bs-target="#carouselExampleIndicators" data-slide-to="2" class=""></li>
                                                </ol>
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/10.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/8.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/1.jpg" alt="" />
                                                    </div>
                                                </div>
                                                <a class="carousel-control-prev" href="#carouselExampleIndicators" role="button" data-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </a>
                                                <a class="carousel-control-next" href="#carouselExampleIndicators" role="button" data-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
    <pre>
    <code class="language-html" data-lang="html">&lt;div id=&quot;carouselExampleIndicators&quot; class=&quot;carousel slide&quot; data-ride=&quot;carousel&quot;&gt;
        &lt;ol class=&quot;carousel-indicators&quot;&gt;
            &lt;li data-target=&quot;#carouselExampleIndicators&quot; data-slide-to=&quot;0&quot; class=&quot;active&quot;&gt;&lt;/li&gt;
            &lt;li data-target=&quot;#carouselExampleIndicators&quot; data-slide-to=&quot;1&quot; class=&quot;&quot;&gt;&lt;/li&gt;
            &lt;li data-target=&quot;#carouselExampleIndicators&quot; data-slide-to=&quot;2&quot; class=&quot;&quot;&gt;&lt;/li&gt;
        &lt;/ol&gt;
        &lt;div class=&quot;carousel-inner&quot;&gt;
            &lt;div class=&quot;carousel-item active&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/10.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/8.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/1.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;a class=&quot;carousel-control-prev&quot; href=&quot;#carouselExampleIndicators&quot; role=&quot;button&quot; data-slide=&quot;prev&quot;&gt;
            &lt;span class=&quot;carousel-control-prev-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Previous&lt;/span&gt;
        &lt;/a&gt;
        &lt;a class=&quot;carousel-control-next&quot; href=&quot;#carouselExampleIndicators&quot; role=&quot;button&quot; data-slide=&quot;next&quot;&gt;
            &lt;span class=&quot;carousel-control-next-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Next&lt;/span&gt;
        &lt;/a&gt;
    &lt;/div&gt;</code>
    </pre>
                                </div>
                                
                                <h3 id="with-captions">With captions</h3>
                                <p>Add captions to your slides easily with the <code>.carousel-caption</code> element within any <code>.carousel-item</code>. They can be easily hidden on smaller viewports, as shown below, with optional <a href="https://v5.getbootstrap.com/docs/5.0/utilities/display/">display utilities</a>. We hide them initially with <code>.d-none</code> and bring them back on medium-sized devices with <code>.d-md-block</code>.</p>
                                <div class="bd-example mb-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="carouselExampleCaptions" class="carousel slide" data-ride="carousel">
                                                <ol class="carousel-indicators">
                                                    <li data-bs-target="#carouselExampleCaptions" data-slide-to="0" class="active"></li>
                                                    <li data-bs-target="#carouselExampleCaptions" data-slide-to="1" class=""></li>
                                                    <li data-bs-target="#carouselExampleCaptions" data-slide-to="2" class=""></li>
                                                </ol>
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/1.jpg" alt="" />
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>First slide label</h5>
                                                            <p>Nulla vitae elit libero, a pharetra augue mollis interdum.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/2.jpg" alt="" />
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Second slide label</h5>
                                                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/3.jpg" alt="" />
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Third slide label</h5>
                                                            <p>Praesent commodo cursus magna, vel scelerisque nisl consectetur.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <a class="carousel-control-prev" href="#carouselExampleCaptions" role="button" data-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </a>
                                                <a class="carousel-control-next" href="#carouselExampleCaptions" role="button" data-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
    <pre>
    <code class="language-html" data-lang="html">&lt;div id=&quot;carouselExampleCaptions&quot; class=&quot;carousel slide&quot; data-ride=&quot;carousel&quot;&gt;
        &lt;ol class=&quot;carousel-indicators&quot;&gt;
            &lt;li data-target=&quot;#carouselExampleCaptions&quot; data-slide-to=&quot;0&quot; class=&quot;active&quot;&gt;&lt;/li&gt;
            &lt;li data-target=&quot;#carouselExampleCaptions&quot; data-slide-to=&quot;1&quot; class=&quot;&quot;&gt;&lt;/li&gt;
            &lt;li data-target=&quot;#carouselExampleCaptions&quot; data-slide-to=&quot;2&quot; class=&quot;&quot;&gt;&lt;/li&gt;
        &lt;/ol&gt;
        &lt;div class=&quot;carousel-inner&quot;&gt;
            &lt;div class=&quot;carousel-item active&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/1.jpg&quot; alt=&quot;&quot; /&gt;
                &lt;div class=&quot;carousel-caption d-none d-md-block&quot;&gt;
                    &lt;h5&gt;First slide label&lt;/h5&gt;
                    &lt;p&gt;Nulla vitae elit libero, a pharetra augue mollis interdum.&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/2.jpg&quot; alt=&quot;&quot; /&gt;
                &lt;div class=&quot;carousel-caption d-none d-md-block&quot;&gt;
                    &lt;h5&gt;Second slide label&lt;/h5&gt;
                    &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipiscing elit.&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/3.jpg&quot; alt=&quot;&quot; /&gt;
                &lt;div class=&quot;carousel-caption d-none d-md-block&quot;&gt;
                    &lt;h5&gt;Third slide label&lt;/h5&gt;
                    &lt;p&gt;Praesent commodo cursus magna, vel scelerisque nisl consectetur.&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;a class=&quot;carousel-control-prev&quot; href=&quot;#carouselExampleCaptions&quot; role=&quot;button&quot; data-slide=&quot;prev&quot;&gt;
            &lt;span class=&quot;carousel-control-prev-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Previous&lt;/span&gt;
        &lt;/a&gt;
        &lt;a class=&quot;carousel-control-next&quot; href=&quot;#carouselExampleCaptions&quot; role=&quot;button&quot; data-slide=&quot;next&quot;&gt;
            &lt;span class=&quot;carousel-control-next-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Next&lt;/span&gt;
        &lt;/a&gt;
    &lt;/div&gt;</code>
    </pre>
                                </div>
                                
                                <h3 id="crossfade">Crossfade</h3>
                                <p>Add <code>.carousel-fade</code> to your carousel to animate slides with a fade transition instead of a slide.</p>
                                <div class="bd-example mb-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="carouselExampleFade" class="carousel slide carousel-fade" data-ride="carousel">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/10.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/8.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/1.jpg" alt="" />
                                                    </div>
                                                </div>
                                                <a class="carousel-control-prev" href="#carouselExampleFade" role="button" data-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </a>
                                                <a class="carousel-control-next" href="#carouselExampleFade" role="button" data-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
    <pre>
    <code class="language-html" data-lang="html">&lt;div id=&quot;carouselExampleFade&quot; class=&quot;carousel slide carousel-fade&quot; data-ride=&quot;carousel&quot;&gt;
        &lt;div class=&quot;carousel-inner&quot;&gt;
            &lt;div class=&quot;carousel-item active&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/10.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/8.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/1.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;a class=&quot;carousel-control-prev&quot; href=&quot;#carouselExampleFade&quot; role=&quot;button&quot; data-slide=&quot;prev&quot;&gt;
            &lt;span class=&quot;carousel-control-prev-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Previous&lt;/span&gt;
        &lt;/a&gt;
        &lt;a class=&quot;carousel-control-next&quot; href=&quot;#carouselExampleFade&quot; role=&quot;button&quot; data-slide=&quot;next&quot;&gt;
            &lt;span class=&quot;carousel-control-next-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Next&lt;/span&gt;
        &lt;/a&gt;
    &lt;/div&gt;</code>
    </pre>
                                </div>
                                
                                <h3 id="individual-carousel-item-interval">Individual <code>.carousel-item</code> interval</h3>
                                <p>Add <code>data-interval=""</code> to a <code>.carousel-item</code> to change the amount of time to delay between automatically cycling to the next item.</p>
                                <div class="bd-example mb-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="carouselExampleInterval" class="carousel slide" data-ride="carousel">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item" data-interval="10000">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/2.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item active carousel-item-left" data-interval="2000">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/5.jpg" alt="" />
                                                    </div>
                                                    <div class="carousel-item carousel-item-next carousel-item-left">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/7.jpg" alt="" />
                                                    </div>
                                                </div>
                                                <a class="carousel-control-prev" href="#carouselExampleInterval" role="button" data-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </a>
                                                <a class="carousel-control-next" href="#carouselExampleInterval" role="button" data-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
    <pre>
    <code class="language-html" data-lang="html">&lt;div id=&quot;carouselExampleInterval&quot; class=&quot;carousel slide&quot; data-ride=&quot;carousel&quot;&gt;
        &lt;div class=&quot;carousel-inner&quot;&gt;
            &lt;div class=&quot;carousel-item&quot; data-interval=&quot;10000&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/2.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item active carousel-item-left&quot; data-interval=&quot;2000&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/5.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item carousel-item-next carousel-item-left&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/7.jpg&quot; alt=&quot;&quot; /&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;a class=&quot;carousel-control-prev&quot; href=&quot;#carouselExampleInterval&quot; role=&quot;button&quot; data-slide=&quot;prev&quot;&gt;
            &lt;span class=&quot;carousel-control-prev-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Previous&lt;/span&gt;
        &lt;/a&gt;
        &lt;a class=&quot;carousel-control-next&quot; href=&quot;#carouselExampleInterval&quot; role=&quot;button&quot; data-slide=&quot;next&quot;&gt;
            &lt;span class=&quot;carousel-control-next-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Next&lt;/span&gt;
        &lt;/a&gt;
    &lt;/div&gt;</code>
    </pre>
                                </div>
                                
                                <h2 id="dark-variant">Dark variant</h2>
                                <p>Add <code>.carousel-dark</code> to the <code>.carousel</code> for darker controls, indicators, and captions. Controls have been inverted from their default white fill with the <code>filter</code> CSS property. Captions and controls have additional Sass variables that customize the <code>color</code> and <code>background-color</code>.</p>
                                <div class="bd-example mb-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="carouselExampleDark" class="carousel carousel-dark slide" data-ride="carousel">
                                                <ol class="carousel-indicators">
                                                    <li data-bs-target="#carouselExampleDark" data-slide-to="0" class=""></li>
                                                    <li data-bs-target="#carouselExampleDark" data-slide-to="1" class=""></li>
                                                    <li data-bs-target="#carouselExampleDark" data-slide-to="2" class="active"></li>
                                                </ol>
                                                <div class="carousel-inner">
                                                    <div class="carousel-item" data-interval="10000">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/3.jpg" alt="" />
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>First slide label</h5>
                                                            <p>Nulla vitae elit libero, a pharetra augue mollis interdum.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item active carousel-item-left" data-interval="2000">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/8.jpg" alt="" />
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Second slide label</h5>
                                                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item carousel-item-next carousel-item-left">
                                                        <img class="img-fluid" src="../../../assets/images/gallery/6.jpg" alt="" />
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Third slide label</h5>
                                                            <p>Praesent commodo cursus magna, vel scelerisque nisl consectetur.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <a class="carousel-control-prev" href="#carouselExampleDark" role="button" data-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </a>
                                                <a class="carousel-control-next" href="#carouselExampleDark" role="button" data-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
    <pre>
    <code class="language-html" data-lang="html">&lt;div id=&quot;carouselExampleDark&quot; class=&quot;carousel carousel-dark slide&quot; data-ride=&quot;carousel&quot;&gt;
        &lt;ol class=&quot;carousel-indicators&quot;&gt;
            &lt;li data-target=&quot;#carouselExampleDark&quot; data-slide-to=&quot;0&quot; class=&quot;&quot;&gt;&lt;/li&gt;
            &lt;li data-target=&quot;#carouselExampleDark&quot; data-slide-to=&quot;1&quot; class=&quot;&quot;&gt;&lt;/li&gt;
            &lt;li data-target=&quot;#carouselExampleDark&quot; data-slide-to=&quot;2&quot; class=&quot;active&quot;&gt;&lt;/li&gt;
        &lt;/ol&gt;
        &lt;div class=&quot;carousel-inner&quot;&gt;
            &lt;div class=&quot;carousel-item&quot; data-interval=&quot;10000&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/3.jpg&quot; alt=&quot;&quot; /&gt;
                &lt;div class=&quot;carousel-caption d-none d-md-block&quot;&gt;
                    &lt;h5&gt;First slide label&lt;/h5&gt;
                    &lt;p&gt;Nulla vitae elit libero, a pharetra augue mollis interdum.&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item active carousel-item-left&quot; data-interval=&quot;2000&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/8.jpg&quot; alt=&quot;&quot; /&gt;
                &lt;div class=&quot;carousel-caption d-none d-md-block&quot;&gt;
                    &lt;h5&gt;Second slide label&lt;/h5&gt;
                    &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipiscing elit.&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class=&quot;carousel-item carousel-item-next carousel-item-left&quot;&gt;
                &lt;img class=&quot;img-fluid&quot; src=&quot;assets/images/gallery/6.jpg&quot; alt=&quot;&quot; /&gt;
                &lt;div class=&quot;carousel-caption d-none d-md-block&quot;&gt;
                    &lt;h5&gt;Third slide label&lt;/h5&gt;
                    &lt;p&gt;Praesent commodo cursus magna, vel scelerisque nisl consectetur.&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;a class=&quot;carousel-control-prev&quot; href=&quot;#carouselExampleDark&quot; role=&quot;button&quot; data-slide=&quot;prev&quot;&gt;
            &lt;span class=&quot;carousel-control-prev-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Previous&lt;/span&gt;
        &lt;/a&gt;
        &lt;a class=&quot;carousel-control-next&quot; href=&quot;#carouselExampleDark&quot; role=&quot;button&quot; data-slide=&quot;next&quot;&gt;
            &lt;span class=&quot;carousel-control-next-icon&quot; aria-hidden=&quot;true&quot;&gt;&lt;/span&gt;
            &lt;span class=&quot;visually-hidden&quot;&gt;Next&lt;/span&gt;
        &lt;/a&gt;
    &lt;/div&gt;</code>
    </pre>
                                </div>
                                
                                <h2 id="usage">Usage</h2>
                                <h3 id="via-data-attributes">Via data attributes</h3>
                                <p>Use data attributes to easily control the position of the carousel. <code>data-slide</code> accepts the keywords <code>prev</code> or <code>next</code>, which alters the slide position relative to its current position. Alternatively, use <code>data-slide-to</code> to pass a raw slide index to the carousel <code>data-slide-to="2"</code>, which shifts the slide position to a particular index beginning with <code>0</code>.</p>
                                <p>The <code>data-ride="carousel"</code> attribute is used to mark a carousel as animating starting at page load. If you don’t use <code>data-ride="carousel"</code> to initialize your carousel, you have to initialize it yourself. <strong>It cannot be used in combination with (redundant and unnecessary) explicit JavaScript initialization of the same carousel.</strong></p>
                                
                                <h3 id="via-javascript">Via JavaScript</h3>
                                <p>Call carousel manually with:</p>
                                <div class="bd-example mb-5">
    <pre><code class="language-js" data-lang="js"><span class="kd">var</span> <span class="nx">myCarousel</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nx">querySelector</span><span class="p">(</span><span class="s1">'#myCarousel'</span><span class="p">)</span>
    <span class="kd">var</span> <span class="nx">carousel</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">bootstrap</span><span class="p">.</span><span class="nx">Carousel</span><span class="p">(</span><span class="nx">myCarousel</span><span class="p">)</span>
    </code></pre>
                                </div>
                                
                                <h3 id="options">Options</h3>
                                <p>Options can be passed via data attributes or JavaScript. For data attributes, append the option name to <code>data-</code>, as in <code>data-interval=""</code>.</p>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th style="width: 100px;">Name</th>
                                            <th style="width: 50px;">Type</th>
                                            <th style="width: 50px;">Default</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>interval</code></td>
                                            <td>number</td>
                                            <td><code>5000</code></td>
                                            <td>The amount of time to delay between automatically cycling an item. If false, carousel will not automatically cycle.</td>
                                        </tr>
                                        <tr>
                                            <td><code>keyboard</code></td>
                                            <td>boolean</td>
                                            <td><code>true</code></td>
                                            <td>Whether the carousel should react to keyboard events.</td>
                                        </tr>
                                        <tr>
                                            <td><code>pause</code></td>
                                            <td>string | boolean</td>
                                            <td><code>"hover"</code></td>
                                            <td><p>If set to <code>"hover"</code>, pauses the cycling of the carousel on <code>mouseenter</code> and resumes the cycling of the carousel on <code>mouseleave</code>. If set to <code>false</code>, hovering over the carousel won't pause it.</p>
                                            <p>On touch-enabled devices, when set to <code>"hover"</code>, cycling will pause on <code>touchend</code> (once the user finished interacting with the carousel) for two intervals, before automatically resuming. Note that this is in addition to the above mouse behavior.</p></td>
                                        </tr>
                                        <tr>
                                            <td><code>slide</code></td>
                                            <td>string | boolean</td>
                                            <td><code>false</code></td>
                                            <td>Autoplays the carousel after the user manually cycles the first item. If "carousel", autoplays the carousel on load.</td>
                                        </tr>
                                        <tr>
                                            <td><code>wrap</code></td>
                                            <td>boolean</td>
                                            <td><code>true</code></td>
                                            <td>Whether the carousel should cycle continuously or have hard stops.</td>
                                        </tr>
                                        <tr>
                                            <td><code>touch</code></td>
                                            <td>boolean</td>
                                            <td><code>true</code></td>
                                            <td>Whether the carousel should support left/right swipe interactions on touchscreen devices.</td>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <h3 id="methods">Methods</h3>
                                <div class="card card-callout">
                                    <div class="card-body">
                                        <h4 id="asynchronous-methods-and-transitions">Asynchronous methods and transitions</h4>
                                        <p>All API methods are <strong>asynchronous</strong> and start a <strong>transition</strong>. They return to the caller as soon as the transition is started but <strong>before it ends</strong>. In addition, a method call on a <strong>transitioning component will be ignored</strong>.</p>
                                        <p><a href="https://v5.getbootstrap.com/docs/5.0/getting-started/javascript/#asynchronous-functions-and-transitions">See our JavaScript documentation for more information</a>.</p>
                                    </div>
                                </div>
                                
                                <p>You can create a carousel instance with the carousel constructor, for example, to initialize with additional options and start cycling through items:</p>
                                <div class="bd-example mb-5">
    <pre><code class="language-js" data-lang="js"><span class="kd">var</span> <span class="nx">myCarousel</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nx">querySelector</span><span class="p">(</span><span class="s1">'#myCarousel'</span><span class="p">)</span>
    <span class="kd">var</span> <span class="nx">carousel</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">bootstrap</span><span class="p">.</span><span class="nx">Carousel</span><span class="p">(</span><span class="nx">myCarousel</span><span class="p">,</span> <span class="p">{</span>
        <span class="nx">interval</span><span class="o">:</span> <span class="mi">2000</span><span class="p">,</span>
        <span class="nx">wrap</span><span class="o">:</span> <span class="kc">false</span>
    <span class="p">})</span>
    </code></pre>
                                </div>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Method</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>cycle</code></td>
                                            <td>Cycles through the carousel items from left to right.</td>
                                        </tr>
                                        <tr>
                                            <td><code>pause</code></td>
                                            <td>Stops the carousel from cycling through items.</td>
                                        </tr>
                                        <tr>
                                            <td><code>prev</code></td>
                                            <td>Cycles to the previous item. <strong>Returns to the caller before the previous item has been shown</strong> (e.g., before the <code>slid.bs.carousel</code> event occurs).</td>
                                        </tr>
                                        <tr>
                                            <td><code>next</code></td>
                                            <td>Cycles to the next item. <strong>Returns to the caller before the next item has been shown</strong> (e.g., before the <code>slid.bs.carousel</code> event occurs).</td>
                                        </tr>
                                        <tr>
                                            <td><code>nextWhenVisible</code></td>
                                            <td>Cycles the carousel to a particular frame (0 based, similar to an array). <strong>Returns to the caller before the target item has been shown</strong> (e.g., before the <code>slid.bs.carousel</code> event occurs).</td>
                                        </tr>
                                        <tr>
                                            <td><code>dispose</code></td>
                                            <td>Destroys an element's carousel.</td>
                                        </tr>
                                        <tr>
                                            <td><code>getInstance</code></td>
                                            <td>Static method which allows you to get the carousel instance associated with a DOM element.</td>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <h3 id="events">Events</h3>
                                <p>Bootstrap’s carousel class exposes two events for hooking into carousel functionality. Both events have the following additional properties:</p>
                                <ul>
                                    <li><code>direction</code>: The direction in which the carousel is sliding (either <code>"left"</code> or <code>"right"</code>).</li>
                                    <li><code>relatedTarget</code>: The DOM element that is being slid into place as the active item.</li>
                                    <li><code>from</code>: The index of the current item</li>
                                    <li><code>to</code>: The index of the next item</li>
                                </ul>
                                <p>All carousel events are fired at the carousel itself (i.e. at the <code>&lt;div class="carousel"&gt;</code>).</p>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th style="width: 150px;">Event type</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>slide.bs.carousel</code></td>
                                            <td>Fires immediately when the <code>slide</code> instance method is invoked.</td>
                                        </tr>
                                        <tr>
                                            <td><code>slid.bs.carousel</code></td>
                                            <td>Fired when the carousel has completed its slide transition.</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="bd-example mb-5">
    <pre class="chroma"><code class="language-js" data-lang="js"><span class="kd">var</span> <span class="nx">myCarousel</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nx">getElementById</span><span class="p">(</span><span class="s1">'myCarousel'</span><span class="p">)</span>

    <span class="nx">myCarousel</span><span class="p">.</span><span class="nx">addEventListener</span><span class="p">(</span><span class="s1">'slide.bs.carousel'</span><span class="p">,</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
        <span class="c1">// do something...
    </span><span class="c1"></span><span class="p">})</span>
    </code></pre>
                                </div>
                                <h3 id="change-transition-duration">Change transition duration</h3>
                                <p>The transition duration of <code>.carousel-item</code> can be changed with the <code>$carousel-transition</code> Sass variable before compiling or custom styles if you’re using the compiled CSS. If multiple transitions are applied, make sure the transform transition is defined first (eg. <code>transition: transform 2s ease, opacity .5s ease-out</code>).</p>
                                
                            </div>
                            <div class="col-lg-3 col-sm-12 d-none d-sm-block">
                                <div class="sticky-lg-top">
                                    <strong class="d-block h6 my-2 pb-2 border-bottom">On this page</strong>
                                    <nav>
                                        <ul>
                                            <li><a href="#how-it-works">How it works</a></li>
                                            <li><a href="#example">Example</a>
                                                <ul>
                                                    <li><a href="#slides-only">Slides only</a></li>
                                                    <li><a href="#with-controls">With controls</a></li>
                                                    <li><a href="#with-indicators">With indicators</a></li>
                                                    <li><a href="#with-captions">With captions</a></li>
                                                    <li><a href="#crossfade">Crossfade</a></li>
                                                    <li><a href="#individual-carousel-item-interval">Individual <code>.carousel-item</code> interval</a></li>
                                                </ul>
                                            </li>
                                            <li><a href="#dark-variant">Dark variant</a></li>
                                            <li><a href="#usage">Usage</a>
                                                <ul>
                                                    <li><a href="#via-data-attributes">Via data attributes</a></li>
                                                    <li><a href="#via-javascript">Via JavaScript</a></li>
                                                    <li><a href="#options">Options</a></li>
                                                    <li><a href="#methods">Methods</a></li>
                                                    <li><a href="#events">Events</a></li>
                                                    <li><a href="#change-transition-duration">Change transition duration</a></li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div> <!-- Row end  -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer">
            <div class="container-fluid">
                <div class="col-12">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row justify-content-between align-items-center">
                                <div class="col">
                                    <p class="mb-0">© AL-UI. <span class="d-none d-sm-inline-block">2021 ThemeMakker.</span></p>
                                </div>
                                <div class="col-auto">
                                    <div class="d-flex justify-content-end">
                                        <!-- List Dot -->
                                        <ul class="list-inline mb-0">
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/about/">About</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/hire-us/">Hire us</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/all-templates/">Template</a>
                                            </li>
                                
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://themeforest.net/licenses/standard" target="_blank">License</a>
                                            </li>
                                        </ul>
                                        <!-- End List Dot -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title">Ready to build Layouts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-c/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-e/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-k/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-k.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Body Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-p/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-p.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Background BG</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-m/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-m.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Header <span class="small">(Fixed)</span></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">AL-UI Setting</h5>
                    </div>
                    <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan" class="active"><div class="cyan"></div></li>
                            <li data-theme="green"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-1">
                                <input class="form-check-input" type="checkbox" id="CheckGradient">
                                <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                            </div>
                    </div>
                    <!-- Settings: bg image -->
                    <div class="setting-img mb-3">
                        <div class="form-check form-switch imagebg-switch mb-1">
                            <input class="form-check-input" type="checkbox" id="CheckImage">
                            <label class="form-check-label" for="CheckImage">Set Background Image (Sidebar)</label>
                        </div>
                        <div class="bg-images">
                            <ul class="list-unstyled d-flex justify-content-between">
                                <li class="sidebar-img-1 sidebar-img-active"><a class="rounded sidebar-img" id="img-1" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-1.jpg" alt="" /></a></li>
                                <li class="sidebar-img-2"><a class="rounded sidebar-img" id="img-2" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-2.jpg" alt="" /></a></li>
                                <li class="sidebar-img-3"><a class="rounded sidebar-img" id="img-3" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-3.jpg" alt="" /></a></li>
                                <li class="sidebar-img-4"><a class="rounded sidebar-img" id="img-4" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-4.jpg" alt="" /></a></li>
                                <li class="sidebar-img-5"><a class="rounded sidebar-img" id="img-5" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-5.jpg" alt="" /></a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>

<!-- Prism js file please do not add in your project -->
<script src="../../../assets/plugin/prismjs/prism.js"></script>

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
</body>
</html>