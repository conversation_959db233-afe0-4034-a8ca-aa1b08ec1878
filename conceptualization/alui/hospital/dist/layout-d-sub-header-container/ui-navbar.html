<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Hospital Management</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">
    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.d.sub.header.min.css">
    <!-- Prism css file please do not add in your project -->
    <link rel="stylesheet" href="../../../assets/plugin/prismjs/prism.css">
</head>

<body>

<div id="layout-d-sub-header" class="theme-cyan">

    <!-- Navigation -->
    <div class="header fixed-top shadow-sm">
        <nav class="navbar navbar-light bg-secondary py-2 py-md-3 px-lg-5 px-md-2">
            <div class="container">

                <!-- Brand -->
                <a href="index.html" class="me-3 me-lg-4 brand-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                        <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
                    </svg>
                </a>

                <!-- Search -->
                <div class="h-left d-none d-sm-block">
                    <div class="input-group border rounded">
                        <button class="btn btn-outline-secondary dropdown-toggle border-0 d-none d-sm-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">Fillter</button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Action</a></li>
                            <li><a class="dropdown-item" href="#">Another action</a></li>
                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Separated link</a></li>
                        </ul>
                        <input type="text" class="form-control bg-transparent border-0" placeholder="Search here...">
                    </div>
                </div>

                <!-- header rightbar icon -->
                <div class="h-right flex-grow-1 justify-content-end d-flex align-items-center me-5 me-lg-0">
                    <div class="d-flex">
                        <a class="nav-link text-primary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                        <a class="nav-link text-primary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                            <i class="fa fa-sliders"></i>
                        </a>
                    </div>
                    <div class="dropdown notifications">
                        <a class="nav-link dropdown-toggle pulse" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-bell"></i>
                            <span class="pulse-ring"></span>
                        </a>
                        <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w380">
                                <div class="card-header border-0 p-3">
                                    <h5 class="mb-0 fw-light d-flex justify-content-between">
                                        <span>Notifications Center</span>
                                        <span class="badge text-muted">14</span>
                                    </h5>
                                    <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tab-content card-body custom_scroll">
                                    <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Chris Morise</span> <small>2MIN</small></p>
                                                        <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Kelly</span> <small>1DAY</small></p>
                                                        <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">making it over 2000 years old</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">savera</span> <small>1DAY</small></p>
                                                        <span class="text-muted">The generated Lorem Ipsum</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                        <small class="text-muted">10:00 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                        <small class="text-muted">04:00 PM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                        <h4>No Logs right now!</h4>
                                    </div>
                                </div>
                                <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown user-profile ms-2 ms-sm-3">
                        <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                            <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                        </a>
                        <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w240">
                                <div class="card-body border-bottom">
                                    <div class="d-flex py-1">
                                        <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted"><span class="fw-bold">Chris Morise</span></p>
                                            <small class="text-muted"><EMAIL></small>
                                            <div>
                                                <a href="#" class="card-link">Sign out</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group m-2">
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile & account</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </nav>

        <!-- Sub header: menu -->
        <div class="sub-header">
            <nav class="navbar navbar-expand-lg p-0">
                <div class="container">

                    <!-- menu toggler -->
                    <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mainHeader">
                        <span class="fa fa-bars"></span>
                    </button>

                    <!-- main menu -->
                    <div class="collapse navbar-collapse order-0 py-1 py-md-2" id="mainHeader">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="index.html"><i class="fa fa-dashboard me-2"></i><span>Dashboard</span></a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-slack me-2"></i><span>Apps</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="calendar.html">Calendar</a></li>
                                    <li><a class="dropdown-item" href="chat.html">Chat app</a></li>
                                    <li><a class="dropdown-item" href="#">Inbox</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file me-2"></i><span>Pages</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="profile.html">Profile</a></li>
                                    <li><a class="dropdown-item" href="timeline.html">Timeline</a></li>
                                    <li><a class="dropdown-item" href="imagegallery.html">Image Gallery</a></li>
                                    <li><a class="dropdown-item" href="invoices.html">Invoices</a></li>
                                    <li><a class="dropdown-item" href="pricing.html">Pricing</a></li>
                                    <li><a class="dropdown-item" href="teamsboard.html">Teams Board</a></li>
                                    <li><a class="dropdown-item" href="faqs.html">FAQs</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/w-cards.html">Widget's</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-lock me-2"></i><span>Authentication</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="auth-signin.html">Sign in</a></li>
                                    <li><a class="dropdown-item" href="auth-signup.html">Sign up</a></li>
                                    <li><a class="dropdown-item" href="auth-password-reset.html">Password reset</a></li>
                                    <li><a class="dropdown-item" href="auth-two-step.html">2-Step Authentication</a></li>
                                    <li><a class="dropdown-item" href="auth-404.html">404</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-diamond me-2"></i><span>UI Components</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="ui-alerts.html">Alerts</a></li>
                                    <li><a class="dropdown-item" href="ui-badge.html">Badge</a></li>
                                    <li><a class="dropdown-item" href="ui-breadcrumb.html">Breadcrumb</a></li>
                                    <li><a class="dropdown-item" href="ui-buttons.html">Buttons</a></li>
                                    <li><a class="dropdown-item" href="ui-card.html">Card</a></li>
                                    <li><a class="dropdown-item" href="ui-carousel.html">Carousel</a></li>
                                    <li><a class="dropdown-item" href="ui-collapse.html">Collapse</a></li>
                                    <li><a class="dropdown-item" href="ui-dropdowns.html">Dropdowns</a></li>
                                    <li><a class="dropdown-item" href="ui-listgroup.html">List group</a></li>
                                    <li><a class="dropdown-item" href="ui-modal.html">Modal</a></li>
                                    <li><a class="dropdown-item" href="ui-navs.html">Navs</a></li>
                                    <li><a class="dropdown-item active" href="ui-navbar.html">Navbar</a></li>
                                    <li><a class="dropdown-item" href="ui-pagination.html">Pagination</a></li>
                                    <li><a class="dropdown-item" href="ui-popovers.html">Popovers</a></li>
                                    <li><a class="dropdown-item" href="ui-progress.html">Progress</a></li>
                                    <li><a class="dropdown-item" href="ui-scrollspy.html">Scrollspy</a></li>
                                    <li><a class="dropdown-item" href="ui-spinners.html">Spinners</a></li>
                                    <li><a class="dropdown-item" href="ui-toasts.html">Toasts</a></li>
                                    <li><a class="dropdown-item" href="ui-tooltips.html">Tooltips</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file-text me-2"></i><span>Docs</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="../../../documentation/stater-page.html">Stater page</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/index.html">Documentation</a></li>
                                    <li><a class="dropdown-item" href="../changelog.html">Changelog</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                </div>
            </nav>
        </div>
    </div>

    <!-- main body area -->
    <div class="main px-lg-5 px-md-2">

        <!-- Body: Header -->
        <div class="body-header border-bottom d-flex py-lg-3 py-md-2">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <ol class="breadcrumb d-inline-flex bg-transparent p-0 m-0">
                            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                            <li class="breadcrumb-item">Components</li>
                            <li class="breadcrumb-item active">Navbar</li>
                        </ol>
                        <h1 class="h4 mt-1">Navbar</h1>
                    </div>
                    <div class="col-auto">
                        <a href="https://v5.getbootstrap.com/" class="btn btn-dark lift">GetBootstrap</a>
                    </div>
                </div>  <!-- Row end  -->
            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex py-lg-4 py-3">
            <div class="container">
                <div class="col-12">
                    <div class="bd-content">
                        
                        <h2 id="how-it-works">How it works</h2>
                        <p>Here’s what you need to know before getting started with the navbar:</p>
                        <div class="alert alert-danger" role="alert">
                            <strong>Navbar</strong> for more bootstrao components <a href="https://v5.getbootstrap.com/docs/5.0/components/navbar/" target="_blank">Bootstrap Navbar documentation <i class="fa fa-external-link"></i></a>
                        </div>
                        <ul>
                            <li>Navbars require a wrapping <code>.navbar</code> with <code>.navbar-expand{-sm|-md|-lg|-xl|-xxl}</code> for responsive collapsing and <a href="#color-schemes">color scheme</a> classes.</li>
                            <li>Navbars and their contents are fluid by default. Change the <a href="#containers">container</a> to limit their horizontal width in different ways.</li>
                            <li>Use our <a href="https://v5.getbootstrap.com/docs/5.0/utilities/spacing/">spacing</a> and <a href="https://v5.getbootstrap.com/docs/5.0/utilities/flex/">flex</a> utility classes for controlling spacing and alignment within navbars.</li>
                            <li>Navbars are responsive by default, but you can easily modify them to change that. Responsive behavior depends on our Collapse JavaScript plugin.</li>
                            <li>Ensure accessibility by using a <code>&lt;nav&gt;</code> element or, if using a more generic element such as a <code>&lt;div&gt;</code>, add a <code>role="navigation"</code> to every navbar to explicitly identify it as a landmark region for users of assistive technologies.</li>
                            <li>Indicate the current item by using <code>aria-current="page"</code> for the current page or <code>aria-current="true"</code> for the current item in a set.</li>
                        </ul>
                        <div class="card card-callout p-3">
                            <span>The animation effect of this component is dependent on the <code>prefers-reduced-motion</code> media query. See the <a href="https://v5.getbootstrap.com/docs/5.0/getting-started/accessibility/#reduced-motion">reduced motion section of our accessibility documentation</a>.</span>
                        </div>
                        <p>Read on for an example and list of supported sub-components.</p>
                        
                        <div class="border-top mt-5 pt-3">
                            <h4 id="supported-content">Supported content</h4>
                            <p>Navbars come with built-in support for a handful of sub-components. Choose from the following as needed:</p>
                            <ul>
                                <li><code>.navbar-brand</code> for your company, product, or project name.</li>
                                <li><code>.navbar-nav</code> for a full-height and lightweight navigation (including support for dropdowns).</li>
                                <li><code>.navbar-toggler</code> for use with our collapse plugin and other <a href="#responsive-behaviors">navigation toggling</a> behaviors.</li>
                                <li>Flex and spacing utilities for any form controls and actions.</li>
                                <li><code>.navbar-text</code> for adding vertically centered strings of text.</li>
                                <li><code>.collapse.navbar-collapse</code> for grouping and hiding navbar contents by a parent breakpoint.</li>
                            </ul>
                            <p>Here’s an example of all the sub-components included in a responsive light-themed navbar that automatically collapses at the <code>lg</code> (large) breakpoint.</p>
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview1" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML1" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview1" role="tabpanel">
                                            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                                                <div class="container">
                                                    <a class="navbar-brand" href="#">Navbar</a>
                                                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                                                        <span class="navbar-toggler-icon"></span>
                                                    </button>
                                                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                                                        <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                                                            <li class="nav-item"><a class="nav-link active" aria-current="page" href="#">Home</a></li>
                                                            <li class="nav-item"><a class="nav-link" href="#">Link</a></li>
                                                            <li class="nav-item dropdown">
                                                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                    Dropdown
                                                                </a>
                                                                <ul class="dropdown-menu border-0 shadow" aria-labelledby="navbarDropdown">
                                                                    <li><a class="dropdown-item" href="#">Action</a></li>
                                                                    <li><a class="dropdown-item" href="#">Another action</a></li>
                                                                    <li><hr class="dropdown-divider"></li>
                                                                    <li><a class="dropdown-item" href="#">Something else here</a></li>
                                                                </ul>
                                                            </li>
                                                            <li class="nav-item"><a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Disabled</a></li>
                                                        </ul>
                                                        <form class="d-flex">
                                                            <input class="form-control me-2" type="search" placeholder="Search" aria-label="Search">
                                                            <button class="btn btn-outline-success" type="submit">Search</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </nav>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML1" role="tabpanel">
<pre class="language-html m-0" data-lang="html">
<code>&lt;nav class=&quot;navbar navbar-expand-lg navbar-light bg-light&quot;&gt;
    &lt;div class=&quot;container-fluid&quot;&gt;
        &lt;a class=&quot;navbar-brand&quot; href=&quot;#&quot;&gt;Navbar&lt;/a&gt;
        &lt;button class=&quot;navbar-toggler&quot; type=&quot;button&quot; data-toggle=&quot;collapse&quot; data-target=&quot;#navbarSupportedContent&quot; aria-controls=&quot;navbarSupportedContent&quot; aria-expanded=&quot;false&quot; aria-label=&quot;Toggle navigation&quot;&gt;
            &lt;span class=&quot;navbar-toggler-icon&quot;&gt;&lt;/span&gt;
        &lt;/button&gt;
        &lt;div class=&quot;collapse navbar-collapse&quot; id=&quot;navbarSupportedContent&quot;&gt;
            &lt;ul class=&quot;navbar-nav me-auto mb-2 mb-lg-0&quot;&gt;
                &lt;li class=&quot;nav-item&quot;&gt;&lt;a class=&quot;nav-link active&quot; aria-current=&quot;page&quot; href=&quot;#&quot;&gt;Home&lt;/a&gt;&lt;/li&gt;
                &lt;li class=&quot;nav-item&quot;&gt;&lt;a class=&quot;nav-link&quot; href=&quot;#&quot;&gt;Link&lt;/a&gt;&lt;/li&gt;
                &lt;li class=&quot;nav-item dropdown&quot;&gt;
                    &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; id=&quot;navbarDropdown&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                        Dropdown
                    &lt;/a&gt;
                    &lt;ul class=&quot;dropdown-menu border-0 shadow&quot; aria-labelledby=&quot;navbarDropdown&quot;&gt;
                        &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Action&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Another action&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;hr class=&quot;dropdown-divider&quot;&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Something else here&lt;/a&gt;&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/li&gt;
                &lt;li class=&quot;nav-item&quot;&gt;&lt;a class=&quot;nav-link disabled&quot; href=&quot;#&quot; tabindex=&quot;-1&quot; aria-disabled=&quot;true&quot;&gt;Disabled&lt;/a&gt;&lt;/li&gt;
            &lt;/ul&gt;
            &lt;form class=&quot;d-flex&quot;&gt;
                &lt;input class=&quot;form-control me-2&quot; type=&quot;search&quot; placeholder=&quot;Search&quot; aria-label=&quot;Search&quot;&gt;
                &lt;button class=&quot;btn btn-outline-success&quot; type=&quot;submit&quot;&gt;Search&lt;/button&gt;
            &lt;/form&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/nav&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p>This example uses <a href="https://v5.getbootstrap.com/docs/5.0/utilities/colors/">color</a> (<code>bg-light</code>) and <a href="https://v5.getbootstrap.com/docs/5.0/utilities/spacing/">spacing</a> (<code>my-2</code>, <code>my-lg-0</code>, <code>me-sm-0</code>, <code>my-sm-0</code>) utility classes.</p>
                            
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview2" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML2" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview2" role="tabpanel">
                                            <div class="header">
                                                <nav class="navbar navbar-light navbar-expand-lg">
                                                    <div class="container">
                                        
                                                        <!-- Brand -->
                                                        <a href="index.html" class="me-3 me-lg-4 brand-icon">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                                                                <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"></path>
                                                            </svg>
                                                        </a>
                                        
                                                        <!-- header rightbar icon -->
                                                        <div class="h-right d-flex align-items-center me-5 me-lg-0 order-1">
                                                            <div class="d-flex">
                                                                <a class="nav-link text-muted collapsed" data-bs-toggle="collapse" data-bs-target="#main-search" href="#" title="Search this chat" aria-expanded="false">
                                                                    <i class="fa fa-search"></i>
                                                                </a>
                                                                <a class="nav-link text-primary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                                                                    <i class="fa fa-sliders"></i>
                                                                </a>
                                                                <a class="nav-link text-primary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                                                            </div>
                                                            <div class="dropdown notifications">
                                                                <a class="nav-link dropdown-toggle pulse" href="#" role="button" data-bs-toggle="dropdown">
                                                                    <i class="fa fa-bell"></i>
                                                                    <span class="pulse-ring"></span>
                                                                </a>
                                                                <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                                                                    <div class="card border-0 w380">
                                                                        <div class="card-header border-0 p-3">
                                                                            <h5 class="mb-0 fw-light d-flex justify-content-between">
                                                                                <span>Notifications Center</span>
                                                                                <span class="badge text-muted">14</span>
                                                                            </h5>
                                                                            <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                                                                <li class="nav-item">
                                                                                    <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                                                                </li>
                                                                                <li class="nav-item">
                                                                                    <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                                                                </li>
                                                                                <li class="nav-item">
                                                                                    <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                        <div class="tab-content card-body custom_scroll">
                                                                            <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                                                                <ul class="list-unstyled list mb-0">
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Chris Morise</span> <small>2MIN</small></p>
                                                                                                <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                                                                <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                                                                <span class="text-muted">There are many variations of passages</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Kelly</span> <small>1DAY</small></p>
                                                                                                <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                                                                <span class="text-muted">making it over 2000 years old</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                                                                <span class="text-muted">There are many variations of passages</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">savera</span> <small>1DAY</small></p>
                                                                                                <span class="text-muted">The generated Lorem Ipsum</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                </ul>
                                                                            </div>
                                                                            <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                                                                <ul class="list-unstyled list mb-0">
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                                                                <small class="text-muted">10:00 AM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                                                                <small class="text-muted">11:30 AM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                                                                <small class="text-muted">04:00 PM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                                                                <small class="text-muted">Yesterday</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                                                                <small class="text-muted">11:30 AM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                </ul>
                                                                            </div>
                                                                            <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                                                                <h4>No Logs right now!</h4>
                                                                            </div>
                                                                        </div>
                                                                        <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="dropdown user-profile ms-2 ms-sm-3">
                                                                <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                                                                    <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                                                                </a>
                                                                <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                                                                    <div class="card border-0 w240">
                                                                        <div class="card-body border-bottom">
                                                                            <div class="d-flex py-1">
                                                                                <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                                                                <div class="flex-fill ms-3">
                                                                                    <p class="mb-0 text-muted"><span class="fw-bold">Chris Morise</span></p>
                                                                                    <small class="text-muted"><EMAIL></small>
                                                                                    <div>
                                                                                        <a href="#" class="card-link">Sign out</a>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="list-group m-2">
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile &amp; account</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                        
                                                        <!-- menu toggler -->
                                                        <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mainHeader">
                                                            <span class="fa fa-bars"></span>
                                                        </button>
                                        
                                                        <!-- main menu -->
                                                        <div class="collapse navbar-collapse order-0" id="mainHeader">
                                                            <ul class="navbar-nav me-auto">
                                                                <li class="nav-item">
                                                                    <a class="nav-link active" href="#"><i class="fa fa-dashboard me-2"></i><span>Dashboard</span></a>
                                                                </li>
                                                                <li class="nav-item dropdown">
                                                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                        <i class="fa fa-slack me-2"></i><span>Apps</span>
                                                                    </a>
                                                                    <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                        <li><a class="dropdown-item" href="#">Calendar</a></li>
                                                                        <li><a class="dropdown-item" href="#">Chat app</a></li>
                                                                        <li><a class="dropdown-item" href="#">Inbox</a></li>
                                                                    </ul>
                                                                </li>
                                                                <li class="nav-item dropdown">
                                                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                        <i class="fa fa-file me-2"></i><span>Pages</span>
                                                                    </a>
                                                                    <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                        <li><a class="dropdown-item" href="profile.html">Profile</a></li>
                                                                        <li><a class="dropdown-item" href="timeline.html">Timeline</a></li>
                                                                        <li><a class="dropdown-item" href="imagegallery.html">Image Gallery</a></li>
                                                                        <li><a class="dropdown-item" href="invoices.html">Invoices</a></li>
                                                                        <li><a class="dropdown-item" href="pricing.html">Pricing</a></li>
                                                                        <li><a class="dropdown-item" href="teamsboard.html">Teams Board</a></li>
                                                                        <li><a class="dropdown-item" href="faqs.html">FAQs</a></li>
                                                                        <li><a class="dropdown-item" href="#">Widget's</a></li>
                                                                    </ul>
                                                                </li>
                                                                <li class="nav-item dropdown">
                                                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                        <i class="fa fa-lock me-2"></i><span>Authentication</span>
                                                                    </a>
                                                                    <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                        <li><a class="dropdown-item" href="auth-signin.html">Sign in</a></li>
                                                                        <li><a class="dropdown-item" href="auth-signup.html">Sign up</a></li>
                                                                        <li><a class="dropdown-item" href="auth-password-reset">Password reset</a></li>
                                                                        <li><a class="dropdown-item" href="auth-two-step">2-Step Authentication</a></li>
                                                                        <li><a class="dropdown-item" href="auth-404">404</a></li>
                                                                    </ul>
                                                                </li>
                                                                <li class="nav-item dropdown">
                                                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                        <i class="fa fa-file-text me-2"></i><span>Docs</span>
                                                                    </a>
                                                                    <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                        <li><a class="dropdown-item" href="#">Stater page</a></li>
                                                                        <li><a class="dropdown-item" href="#">Documentation</a></li>
                                                                        <li><a class="dropdown-item" href="#">Changelog</a></li>
                                                                    </ul>
                                                                </li>
                                                            </ul>
                                                        </div>
                                        
                                                    </div>
                                                </nav>
                                        
                                                <!-- Search: div -->
                                                <div class="border-bottom px-lg-5 px-md-2 collapse bg-primary" id="main-search">
                                                    <div class="container py-4">
                                        
                                                        <div class="input-group">
                                                            <input type="text" class="form-control border-0 p-0 bg-transparent" placeholder="Search. Components, Layouts, etc...">
                                        
                                                            <div class="input-group-append ms-3">
                                                                <button class="btn btn-light" type="submit">Search</button>
                                                            </div>
                                                        </div>
                                        
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML2" role="tabpanel">
<pre class="language-html m-0" data-lang="html">
<code>&lt;div class=&quot;header&quot;&gt;
    &lt;nav class=&quot;navbar navbar-expand-lg&quot;&gt;
        &lt;div class=&quot;container-fluid&quot;&gt;

            &lt;!-- Brand --&gt;
            &lt;a href=&quot;index.html&quot; class=&quot;me-3 me-lg-4 brand-icon&quot;&gt;
                &lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; width=&quot;24&quot; viewBox=&quot;0 0 64 80&quot; fill=&quot;none&quot;&gt;
                    &lt;path d=&quot;M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z&quot; fill=&quot;black&quot;&gt;&lt;/path&gt;
                &lt;/svg&gt;
            &lt;/a&gt;

            &lt;!-- header rightbar icon --&gt;
            &lt;div class=&quot;h-right d-flex align-items-center me-5 me-lg-0 order-1&quot;&gt;
                &lt;div class=&quot;d-flex&quot;&gt;
                    &lt;a class=&quot;nav-link text-muted collapsed&quot; data-toggle=&quot;collapse&quot; data-target=&quot;#main-search&quot; href=&quot;#&quot; title=&quot;Search this chat&quot; aria-expanded=&quot;false&quot;&gt;
                        &lt;i class=&quot;fa fa-search&quot;&gt;&lt;/i&gt;
                    &lt;/a&gt;
                    &lt;a class=&quot;nav-link text-primary&quot; href=&quot;#&quot; data-toggle=&quot;modal&quot; data-target=&quot;#LayoutModal&quot;&gt;
                        &lt;i class=&quot;fa fa-sliders&quot;&gt;&lt;/i&gt;
                    &lt;/a&gt;
                    &lt;a class=&quot;nav-link text-primary&quot; href=&quot;#&quot; title=&quot;Settings&quot; data-toggle=&quot;modal&quot; data-target=&quot;#SettingsModal&quot;&gt;&lt;i class=&quot;fa fa-gear&quot;&gt;&lt;/i&gt;&lt;/a&gt;
                &lt;/div&gt;
                &lt;div class=&quot;dropdown notifications&quot;&gt;
                    &lt;a class=&quot;nav-link dropdown-toggle pulse&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot;&gt;
                        &lt;i class=&quot;fa fa-bell&quot;&gt;&lt;/i&gt;
                        &lt;span class=&quot;pulse-ring&quot;&gt;&lt;/span&gt;
                    &lt;/a&gt;
                    &lt;div id=&quot;NotificationsDiv&quot; class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0&quot;&gt;
                        &lt;div class=&quot;card border-0 w380&quot;&gt;
                            &lt;div class=&quot;card-header border-0 p-3&quot;&gt;
                                &lt;h5 class=&quot;mb-0 fw-light d-flex justify-content-between&quot;&gt;
                                    &lt;span&gt;Notifications Center&lt;/span&gt;
                                    &lt;span class=&quot;badge text-muted&quot;&gt;14&lt;/span&gt;
                                &lt;/h5&gt;
                                &lt;ul class=&quot;nav nav-tabs mt-3 border-bottom-0&quot; role=&quot;tablist&quot;&gt;
                                    &lt;li class=&quot;nav-item&quot;&gt;
                                        &lt;a class=&quot;nav-link fw-light ps-0 me-2 active&quot; data-bs-toggle=&quot;tab&quot; href=&quot;#Noti-tab-Message&quot; role=&quot;tab&quot;&gt;Message&lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li class=&quot;nav-item&quot;&gt;
                                        &lt;a class=&quot;nav-link fw-light me-2&quot; data-bs-toggle=&quot;tab&quot; href=&quot;#Noti-tab-Events&quot; role=&quot;tab&quot;&gt;Events&lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li class=&quot;nav-item&quot;&gt;
                                        &lt;a class=&quot;nav-link fw-light&quot; data-bs-toggle=&quot;tab&quot; href=&quot;#Noti-tab-Logs&quot; role=&quot;tab&quot;&gt;Logs&lt;/a&gt;
                                    &lt;/li&gt;
                                &lt;/ul&gt;
                            &lt;/div&gt;
                            &lt;div class=&quot;tab-content card-body&quot;&gt;
                                &lt;div class=&quot;tab-pane fade show active&quot; id=&quot;Noti-tab-Message&quot; role=&quot;tabpanel&quot;&gt;
                                    &lt;ul class=&quot;list-unstyled list mb-0&quot;&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar1.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Chris Fox&lt;/span&gt; &lt;small&gt;2MIN&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;changed an issue from &quot;In Progress&quot; to &lt;span class=&quot;badge bg-success&quot;&gt;Review&lt;/span&gt;&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded-circle no-thumbnail&quot;&gt;RH&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Dr. Hossein Shams&lt;/span&gt; &lt;small&gt;13MIN&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;It is a long established fact that a reader will be distracted&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar3.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Orlando Lentz&lt;/span&gt; &lt;small&gt;1HR&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;There are many variations of passages&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar4.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Barbara Kelly&lt;/span&gt; &lt;small&gt;1DAY&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;Contrary to popular belief &lt;span class=&quot;badge bg-danger&quot;&gt;Code&lt;/span&gt;&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar5.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Dr. Hossein Shams&lt;/span&gt; &lt;small&gt;13MIN&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;making it over 2000 years old&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar6.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Orlando Lentz&lt;/span&gt; &lt;small&gt;1HR&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;There are many variations of passages&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar7.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Rose Rivera&lt;/span&gt; &lt;small class=&quot;&quot;&gt;1DAY&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;The generated Lorem Ipsum&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                    &lt;/ul&gt;
                                &lt;/div&gt;
                                &lt;div class=&quot;tab-pane fade&quot; id=&quot;Noti-tab-Events&quot; role=&quot;tabpanel&quot;&gt;
                                    &lt;ul class=&quot;list-unstyled list mb-0&quot;&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-info-circle fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Campaign &lt;strong class=&quot;text-primary&quot;&gt;Holiday Sale&lt;/strong&gt; is nearly reach budget limit.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;10:00 AM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-thumbs-up fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Your New Campaign &lt;strong class=&quot;text-primary&quot;&gt;Holiday Sale&lt;/strong&gt; is approved.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;11:30 AM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-pie-chart fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Website visits from Twitter is &lt;strong class=&quot;text-danger&quot;&gt;27% higher&lt;/strong&gt; than last week.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;04:00 PM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-warning fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;&lt;strong class=&quot;text-warning&quot;&gt;Error&lt;/strong&gt; on website analytics configurations&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;Yesterday&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-thumbs-up fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Your New Campaign &lt;strong class=&quot;text-primary&quot;&gt;Holiday Sale&lt;/strong&gt; is approved.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;11:30 AM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                    &lt;/ul&gt;
                                &lt;/div&gt;
                                &lt;div class=&quot;tab-pane fade&quot; id=&quot;Noti-tab-Logs&quot; role=&quot;tabpanel&quot;&gt;
                                    &lt;h4&gt;No Logs right now!&lt;/h4&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;a class=&quot;card-footer text-center border-top-0&quot; href=&quot;#&quot;&gt; View all notifications&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;dropdown user-profile ms-2 ms-sm-3&quot;&gt;
                    &lt;a class=&quot;nav-link dropdown-toggle pulse p-0&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot;&gt;
                        &lt;img class=&quot;avatar rounded-circle img-thumbnail&quot; src=&quot;assets/images/profile_av.png&quot; alt=&quot;&quot;&gt;
                    &lt;/a&gt;
                    &lt;div class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0&quot;&gt;
                        &lt;div class=&quot;card border-0 w240&quot;&gt;
                            &lt;div class=&quot;card-body border-bottom&quot;&gt;
                                &lt;div class=&quot;d-flex py-1&quot;&gt;
                                    &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;../assets/images/profile_av.png&quot; alt=&quot;&quot;&gt;
                                    &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                        &lt;p class=&quot;mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Chris Fox&lt;/span&gt;&lt;/p&gt;
                                        &lt;small class=&quot;text-muted&quot;&gt;<EMAIL>&lt;/small&gt;
                                        &lt;div&gt;
                                            &lt;a href=&quot;#&quot; class=&quot;card-link&quot;&gt;Sign out&lt;/a&gt;
                                        &lt;/div&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;div class=&quot;list-group m-2&quot;&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-user&quot;&gt;&lt;/i&gt;Profile &amp;amp; account&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-gear&quot;&gt;&lt;/i&gt;Settings&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-tag&quot;&gt;&lt;/i&gt;Customization&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-users&quot;&gt;&lt;/i&gt;Manage team&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-calendar&quot;&gt;&lt;/i&gt;My Events&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-credit-card&quot;&gt;&lt;/i&gt;My Statements&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;

            &lt;!-- menu toggler --&gt;
            &lt;button class=&quot;navbar-toggler p-0 border-0&quot; type=&quot;button&quot; data-toggle=&quot;collapse&quot; data-target=&quot;#mainHeader&quot;&gt;
                &lt;span class=&quot;fa fa-bars&quot;&gt;&lt;/span&gt;
            &lt;/button&gt;

            &lt;!-- main menu --&gt;
            &lt;div class=&quot;collapse navbar-collapse order-0&quot; id=&quot;mainHeader&quot;&gt;
                &lt;ul class=&quot;navbar-nav me-auto&quot;&gt;
                    &lt;li class=&quot;nav-item&quot;&gt;
                        &lt;a class=&quot;nav-link active&quot; href=&quot;#&quot;&gt;&lt;i class=&quot;fa fa-dashboard me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Dashboard&lt;/span&gt;&lt;/a&gt;
                    &lt;/li&gt;
                    &lt;li class=&quot;nav-item dropdown&quot;&gt;
                        &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                            &lt;i class=&quot;fa fa-slack me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Apps&lt;/span&gt;
                        &lt;/a&gt;
                        &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Calendar&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Chat app&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Inbox&lt;/a&gt;&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/li&gt;
                    &lt;li class=&quot;nav-item dropdown&quot;&gt;
                        &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                            &lt;i class=&quot;fa fa-file me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Pages&lt;/span&gt;
                        &lt;/a&gt;
                        &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;profile.html&quot;&gt;Profile&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;timeline.html&quot;&gt;Timeline&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;imagegallery.html&quot;&gt;Image Gallery&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;invoices.html&quot;&gt;Invoices&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;pricing.html&quot;&gt;Pricing&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;teamsboard.html&quot;&gt;Teams Board&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;faqs.html&quot;&gt;FAQs&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Widget's&lt;/a&gt;&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/li&gt;
                    &lt;li class=&quot;nav-item dropdown&quot;&gt;
                        &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                            &lt;i class=&quot;fa fa-lock me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Authentication&lt;/span&gt;
                        &lt;/a&gt;
                        &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-signin.html&quot;&gt;Sign in&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-signup.html&quot;&gt;Sign up&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-password-reset&quot;&gt;Password reset&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-two-step&quot;&gt;2-Step Authentication&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-404&quot;&gt;404&lt;/a&gt;&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/li&gt;
                    &lt;li class=&quot;nav-item dropdown&quot;&gt;
                        &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                            &lt;i class=&quot;fa fa-file-text me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Docs&lt;/span&gt;
                        &lt;/a&gt;
                        &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Stater page&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Documentation&lt;/a&gt;&lt;/li&gt;
                            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Changelog&lt;/a&gt;&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/li&gt;
                &lt;/ul&gt;
            &lt;/div&gt;

        &lt;/div&gt;
    &lt;/nav&gt;

    &lt;!-- Search: div --&gt;
    &lt;div class=&quot;border-bottom px-lg-5 px-md-2 collapse bg-primary&quot; id=&quot;main-search&quot;&gt;
        &lt;div class=&quot;container py-4&quot;&gt;

            &lt;div class=&quot;input-group&quot;&gt;
                &lt;input type=&quot;text&quot; class=&quot;form-control border-0 p-0 bg-transparent&quot; placeholder=&quot;Search. Components, Layouts, etc...&quot;&gt;

                &lt;div class=&quot;input-group-append ms-3&quot;&gt;
                    &lt;button class=&quot;btn btn-light&quot; type=&quot;submit&quot;&gt;Search&lt;/button&gt;
                &lt;/div&gt;
            &lt;/div&gt;

        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview3" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML3" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3 bg-transparent">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview3" role="tabpanel">
                                            <div class="header shadow-sm">
                                                <nav class="navbar navbar-light bg-secondary py-2 py-md-3 px-lg-5 px-md-2">
                                                    <div class="container">
                                        
                                                        <!-- Brand -->
                                                        <a href="index.html" class="me-3 me-lg-4 brand-icon">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                                                                <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"></path>
                                                            </svg>
                                                        </a>
                                        
                                                        <!-- Search -->
                                                        <div class="h-left d-none d-sm-block">
                                                            <div class="input-group border rounded">
                                                                <button class="btn btn-outline-secondary dropdown-toggle border-0 d-none d-sm-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">Fillter</button>
                                                                <ul class="dropdown-menu border-0 shadow">
                                                                    <li><a class="dropdown-item" href="#">Action</a></li>
                                                                    <li><a class="dropdown-item" href="#">Another action</a></li>
                                                                    <li><a class="dropdown-item" href="#">Something else here</a></li>
                                                                    <li><hr class="dropdown-divider"></li>
                                                                    <li><a class="dropdown-item" href="#">Separated link</a></li>
                                                                </ul>
                                                                <input type="text" class="form-control bg-transparent border-0" placeholder="Search here...">
                                                            </div>
                                                        </div>
                                        
                                                        <!-- header rightbar icon -->
                                                        <div class="h-right flex-grow-1 justify-content-end d-flex align-items-center me-5 me-lg-0">
                                                            <div class="d-flex">
                                                                <a class="nav-link text-primary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                                                                <a class="nav-link text-primary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                                                                    <i class="fa fa-sliders"></i>
                                                                </a>
                                                            </div>
                                                            <div class="dropdown notifications">
                                                                <a class="nav-link dropdown-toggle pulse" href="#" role="button" data-bs-toggle="dropdown">
                                                                    <i class="fa fa-bell"></i>
                                                                    <span class="pulse-ring"></span>
                                                                </a>
                                                                <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                                                                    <div class="card border-0 w380">
                                                                        <div class="card-header border-0 p-3">
                                                                            <h5 class="mb-0 fw-light d-flex justify-content-between">
                                                                                <span>Notifications Center</span>
                                                                                <span class="badge text-muted">14</span>
                                                                            </h5>
                                                                            <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                                                                <li class="nav-item">
                                                                                    <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                                                                </li>
                                                                                <li class="nav-item">
                                                                                    <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                                                                </li>
                                                                                <li class="nav-item">
                                                                                    <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                        <div class="tab-content card-body custom_scroll">
                                                                            <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                                                                <ul class="list-unstyled list mb-0">
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Chris Morise</span> <small>2MIN</small></p>
                                                                                                <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                                                                <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                                                                <span class="text-muted">There are many variations of passages</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Kelly</span> <small>1DAY</small></p>
                                                                                                <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                                                                <span class="text-muted">making it over 2000 years old</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                                                                <span class="text-muted">There are many variations of passages</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">savera</span> <small>1DAY</small></p>
                                                                                                <span class="text-muted">The generated Lorem Ipsum</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                </ul>
                                                                            </div>
                                                                            <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                                                                <ul class="list-unstyled list mb-0">
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                                                                <small class="text-muted">10:00 AM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                                                                <small class="text-muted">11:30 AM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                                                                <small class="text-muted">04:00 PM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                                                                <small class="text-muted">Yesterday</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="py-2 mb-1 border-bottom">
                                                                                        <a href="javascript:void(0);" class="d-flex">
                                                                                            <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                                                            <div class="flex-fill ms-3">
                                                                                                <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                                                                <small class="text-muted">11:30 AM Today</small>
                                                                                            </div>
                                                                                        </a>
                                                                                    </li>
                                                                                </ul>
                                                                            </div>
                                                                            <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                                                                <h4>No Logs right now!</h4>
                                                                            </div>
                                                                        </div>
                                                                        <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="dropdown user-profile ms-2 ms-sm-3">
                                                                <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                                                                    <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                                                                </a>
                                                                <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                                                                    <div class="card border-0 w240">
                                                                        <div class="card-body border-bottom">
                                                                            <div class="d-flex py-1">
                                                                                <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                                                                <div class="flex-fill ms-3">
                                                                                    <p class="mb-0 text-muted"><span class="fw-bold">Chris Morise</span></p>
                                                                                    <small class="text-muted"><EMAIL></small>
                                                                                    <div>
                                                                                        <a href="#" class="card-link">Sign out</a>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="list-group m-2">
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile &amp; account</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                                                            <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                        
                                                    </div>
                                                </nav>
                                        
                                                <div class="sub-header">
                                                    <nav class="navbar navbar-light navbar-expand-lg p-0">
                                                        <div class="container">
                                        
                                                            <!-- menu toggler -->
                                                            <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mainHeader">
                                                                <span class="fa fa-bars"></span>
                                                            </button>
                                        
                                                            <!-- main menu -->
                                                            <div class="collapse navbar-collapse order-0 py-1 py-md-2" id="mainHeader">
                                                                <ul class="navbar-nav me-auto">
                                                                    <li class="nav-item">
                                                                        <a class="nav-link active" href="#"><i class="fa fa-dashboard me-2"></i><span>Dashboard</span></a>
                                                                    </li>
                                                                    <li class="nav-item dropdown">
                                                                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                            <i class="fa fa-slack me-2"></i><span>Apps</span>
                                                                        </a>
                                                                        <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                            <li><a class="dropdown-item" href="#">Calendar</a></li>
                                                                            <li><a class="dropdown-item" href="#">Chat app</a></li>
                                                                            <li><a class="dropdown-item" href="#">Inbox</a></li>
                                                                        </ul>
                                                                    </li>
                                                                    <li class="nav-item dropdown">
                                                                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                            <i class="fa fa-file me-2"></i><span>Pages</span>
                                                                        </a>
                                                                        <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                            <li><a class="dropdown-item" href="profile.html">Profile</a></li>
                                                                            <li><a class="dropdown-item" href="timeline.html">Timeline</a></li>
                                                                            <li><a class="dropdown-item" href="imagegallery.html">Image Gallery</a></li>
                                                                            <li><a class="dropdown-item" href="invoices.html">Invoices</a></li>
                                                                            <li><a class="dropdown-item" href="pricing.html">Pricing</a></li>
                                                                            <li><a class="dropdown-item" href="teamsboard.html">Teams Board</a></li>
                                                                            <li><a class="dropdown-item" href="faqs.html">FAQs</a></li>
                                                                            <li><a class="dropdown-item" href="#">Widget's</a></li>
                                                                        </ul>
                                                                    </li>
                                                                    <li class="nav-item dropdown">
                                                                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                            <i class="fa fa-lock me-2"></i><span>Authentication</span>
                                                                        </a>
                                                                        <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                            <li><a class="dropdown-item" href="auth-signin.html">Sign in</a></li>
                                                                            <li><a class="dropdown-item" href="auth-signup.html">Sign up</a></li>
                                                                            <li><a class="dropdown-item" href="auth-password-reset">Password reset</a></li>
                                                                            <li><a class="dropdown-item" href="auth-two-step">2-Step Authentication</a></li>
                                                                            <li><a class="dropdown-item" href="auth-404">404</a></li>
                                                                        </ul>
                                                                    </li>
                                                                    <li class="nav-item dropdown">
                                                                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                            <i class="fa fa-file-text me-2"></i><span>Docs</span>
                                                                        </a>
                                                                        <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                                                            <li><a class="dropdown-item" href="#">Stater page</a></li>
                                                                            <li><a class="dropdown-item" href="#">Documentation</a></li>
                                                                            <li><a class="dropdown-item" href="#">Changelog</a></li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                        
                                                        </div>
                                                    </nav>
                                                </div>
                                        
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML3" role="tabpanel">
<pre class="language-html m-0" data-lang="html">
<code>&lt;div class=&quot;header shadow-sm&quot;&gt;
    &lt;nav class=&quot;navbar navbar-light bg-secondary py-2 py-md-3 px-lg-5 px-md-2&quot;&gt;
        &lt;div class=&quot;container-fluid&quot;&gt;

            &lt;!-- Brand --&gt;
            &lt;a href=&quot;index.html&quot; class=&quot;me-3 me-lg-4 brand-icon&quot;&gt;
                &lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; width=&quot;24&quot; viewBox=&quot;0 0 64 80&quot; fill=&quot;none&quot;&gt;
                    &lt;path d=&quot;M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z&quot; fill=&quot;black&quot;&gt;&lt;/path&gt;
                &lt;/svg&gt;
            &lt;/a&gt;

            &lt;!-- Search --&gt;
            &lt;div class=&quot;h-left&quot;&gt;
                &lt;div class=&quot;input-group border rounded&quot;&gt;
                    &lt;button class=&quot;btn btn-outline-secondary dropdown-toggle border-0 d-none d-sm-block&quot; type=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;Fillter&lt;/button&gt;
                    &lt;ul class=&quot;dropdown-menu border-0 shadow&quot;&gt;
                        &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Action&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Another action&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Something else here&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;hr class=&quot;dropdown-divider&quot;&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Separated link&lt;/a&gt;&lt;/li&gt;
                    &lt;/ul&gt;
                    &lt;input type=&quot;text&quot; class=&quot;form-control bg-transparent border-0&quot; placeholder=&quot;Search here...&quot;&gt;
                &lt;/div&gt;
            &lt;/div&gt;

            &lt;!-- header rightbar icon --&gt;
            &lt;div class=&quot;h-right flex-grow-1 justify-content-end d-flex align-items-center me-5 me-lg-0&quot;&gt;
                &lt;div class=&quot;d-flex&quot;&gt;
                    &lt;a class=&quot;nav-link text-primary&quot; href=&quot;#&quot; title=&quot;Settings&quot; data-toggle=&quot;modal&quot; data-target=&quot;#SettingsModal&quot;&gt;&lt;i class=&quot;fa fa-gear&quot;&gt;&lt;/i&gt;&lt;/a&gt;
                    &lt;a class=&quot;nav-link text-primary&quot; href=&quot;#&quot; data-toggle=&quot;modal&quot; data-target=&quot;#LayoutModal&quot;&gt;
                        &lt;i class=&quot;fa fa-sliders&quot;&gt;&lt;/i&gt;
                    &lt;/a&gt;
                &lt;/div&gt;
                &lt;div class=&quot;dropdown notifications&quot;&gt;
                    &lt;a class=&quot;nav-link dropdown-toggle pulse&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot;&gt;
                        &lt;i class=&quot;fa fa-bell&quot;&gt;&lt;/i&gt;
                        &lt;span class=&quot;pulse-ring&quot;&gt;&lt;/span&gt;
                    &lt;/a&gt;
                    &lt;div id=&quot;NotificationsDiv&quot; class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0&quot;&gt;
                        &lt;div class=&quot;card border-0 w380&quot;&gt;
                            &lt;div class=&quot;card-header border-0 p-3&quot;&gt;
                                &lt;h5 class=&quot;mb-0 fw-light d-flex justify-content-between&quot;&gt;
                                    &lt;span&gt;Notifications Center&lt;/span&gt;
                                    &lt;span class=&quot;badge text-muted&quot;&gt;14&lt;/span&gt;
                                &lt;/h5&gt;
                                &lt;ul class=&quot;nav nav-tabs mt-3 border-bottom-0&quot; role=&quot;tablist&quot;&gt;
                                    &lt;li class=&quot;nav-item&quot;&gt;
                                        &lt;a class=&quot;nav-link fw-light ps-0 me-2 active&quot; data-bs-toggle=&quot;tab&quot; href=&quot;#Noti-tab-Message&quot; role=&quot;tab&quot;&gt;Message&lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li class=&quot;nav-item&quot;&gt;
                                        &lt;a class=&quot;nav-link fw-light me-2&quot; data-bs-toggle=&quot;tab&quot; href=&quot;#Noti-tab-Events&quot; role=&quot;tab&quot;&gt;Events&lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li class=&quot;nav-item&quot;&gt;
                                        &lt;a class=&quot;nav-link fw-light&quot; data-bs-toggle=&quot;tab&quot; href=&quot;#Noti-tab-Logs&quot; role=&quot;tab&quot;&gt;Logs&lt;/a&gt;
                                    &lt;/li&gt;
                                &lt;/ul&gt;
                            &lt;/div&gt;
                            &lt;div class=&quot;tab-content card-body&quot;&gt;
                                &lt;div class=&quot;tab-pane fade show active&quot; id=&quot;Noti-tab-Message&quot; role=&quot;tabpanel&quot;&gt;
                                    &lt;ul class=&quot;list-unstyled list mb-0&quot;&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar1.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Chris Fox&lt;/span&gt; &lt;small&gt;2MIN&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;changed an issue from &quot;In Progress&quot; to &lt;span class=&quot;badge bg-success&quot;&gt;Review&lt;/span&gt;&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded-circle no-thumbnail&quot;&gt;RH&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Dr. Hossein Shams&lt;/span&gt; &lt;small&gt;13MIN&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;It is a long established fact that a reader will be distracted&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar3.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Orlando Lentz&lt;/span&gt; &lt;small&gt;1HR&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;There are many variations of passages&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar4.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Barbara Kelly&lt;/span&gt; &lt;small&gt;1DAY&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;Contrary to popular belief &lt;span class=&quot;badge bg-danger&quot;&gt;Code&lt;/span&gt;&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar5.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Dr. Hossein Shams&lt;/span&gt; &lt;small&gt;13MIN&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;making it over 2000 years old&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar6.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Orlando Lentz&lt;/span&gt; &lt;small&gt;1HR&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;There are many variations of passages&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar7.jpg&quot; alt=&quot;&quot;&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;d-flex justify-content-between mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Rose Rivera&lt;/span&gt; &lt;small class=&quot;&quot;&gt;1DAY&lt;/small&gt;&lt;/p&gt;
                                                    &lt;span class=&quot;text-muted&quot;&gt;The generated Lorem Ipsum&lt;/span&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                    &lt;/ul&gt;
                                &lt;/div&gt;
                                &lt;div class=&quot;tab-pane fade&quot; id=&quot;Noti-tab-Events&quot; role=&quot;tabpanel&quot;&gt;
                                    &lt;ul class=&quot;list-unstyled list mb-0&quot;&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-info-circle fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Campaign &lt;strong class=&quot;text-primary&quot;&gt;Holiday Sale&lt;/strong&gt; is nearly reach budget limit.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;10:00 AM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-thumbs-up fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Your New Campaign &lt;strong class=&quot;text-primary&quot;&gt;Holiday Sale&lt;/strong&gt; is approved.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;11:30 AM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-pie-chart fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Website visits from Twitter is &lt;strong class=&quot;text-danger&quot;&gt;27% higher&lt;/strong&gt; than last week.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;04:00 PM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-warning fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;&lt;strong class=&quot;text-warning&quot;&gt;Error&lt;/strong&gt; on website analytics configurations&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;Yesterday&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                        &lt;li class=&quot;py-2 mb-1 border-bottom&quot;&gt;
                                            &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
                                                &lt;div class=&quot;avatar rounded no-thumbnail&quot;&gt;&lt;i class=&quot;fa fa-thumbs-up fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
                                                &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                                    &lt;p class=&quot;mb-0 text-muted&quot;&gt;Your New Campaign &lt;strong class=&quot;text-primary&quot;&gt;Holiday Sale&lt;/strong&gt; is approved.&lt;/p&gt;
                                                    &lt;small class=&quot;text-muted&quot;&gt;11:30 AM Today&lt;/small&gt;
                                                &lt;/div&gt;
                                            &lt;/a&gt;
                                        &lt;/li&gt;
                                    &lt;/ul&gt;
                                &lt;/div&gt;
                                &lt;div class=&quot;tab-pane fade&quot; id=&quot;Noti-tab-Logs&quot; role=&quot;tabpanel&quot;&gt;
                                    &lt;h4&gt;No Logs right now!&lt;/h4&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;a class=&quot;card-footer text-center border-top-0&quot; href=&quot;#&quot;&gt; View all notifications&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class=&quot;dropdown user-profile ms-2 ms-sm-3&quot;&gt;
                    &lt;a class=&quot;nav-link dropdown-toggle pulse p-0&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot;&gt;
                        &lt;img class=&quot;avatar rounded-circle img-thumbnail&quot; src=&quot;assets/images/profile_av.png&quot; alt=&quot;&quot;&gt;
                    &lt;/a&gt;
                    &lt;div class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0&quot;&gt;
                        &lt;div class=&quot;card border-0 w240&quot;&gt;
                            &lt;div class=&quot;card-body border-bottom&quot;&gt;
                                &lt;div class=&quot;d-flex py-1&quot;&gt;
                                    &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/profile_av.png&quot; alt=&quot;&quot;&gt;
                                    &lt;div class=&quot;flex-fill ms-3&quot;&gt;
                                        &lt;p class=&quot;mb-0 text-muted&quot;&gt;&lt;span class=&quot;fw-bold;Chris Fox&lt;/span&gt;&lt;/p&gt;
                                        &lt;small class=&quot;text-muted&quot;&gt;<EMAIL>&lt;/small&gt;
                                        &lt;div&gt;
                                            &lt;a href=&quot;#&quot; class=&quot;card-link&quot;&gt;Sign out&lt;/a&gt;
                                        &lt;/div&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;div class=&quot;list-group m-2&quot;&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-user&quot;&gt;&lt;/i&gt;Profile &amp;amp; account&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-gear&quot;&gt;&lt;/i&gt;Settings&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-tag&quot;&gt;&lt;/i&gt;Customization&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-users&quot;&gt;&lt;/i&gt;Manage team&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-calendar&quot;&gt;&lt;/i&gt;My Events&lt;/a&gt;
                                &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action border-0&quot;&gt;&lt;i class=&quot;w30 fa fa-credit-card&quot;&gt;&lt;/i&gt;My Statements&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;

        &lt;/div&gt;
    &lt;/nav&gt;

    &lt;div class=&quot;sub-header&quot;&gt;
        &lt;nav class=&quot;navbar navbar-light navbar-expand-lg p-0&quot;&gt;
            &lt;div class=&quot;container-fluid&quot;&gt;

                &lt;!-- menu toggler --&gt;
                &lt;button class=&quot;navbar-toggler p-0 border-0&quot; type=&quot;button&quot; data-toggle=&quot;collapse&quot; data-target=&quot;#mainHeader&quot;&gt;
                    &lt;span class=&quot;fa fa-bars&quot;&gt;&lt;/span&gt;
                &lt;/button&gt;

                &lt;!-- main menu --&gt;
                &lt;div class=&quot;collapse navbar-collapse order-0 py-1 py-md-2&quot; id=&quot;mainHeader&quot;&gt;
                    &lt;ul class=&quot;navbar-nav me-auto&quot;&gt;
                        &lt;li class=&quot;nav-item&quot;&gt;
                            &lt;a class=&quot;nav-link active&quot; href=&quot;#&quot;&gt;&lt;i class=&quot;fa fa-dashboard me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Dashboard&lt;/span&gt;&lt;/a&gt;
                        &lt;/li&gt;
                        &lt;li class=&quot;nav-item dropdown&quot;&gt;
                            &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                                &lt;i class=&quot;fa fa-slack me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Apps&lt;/span&gt;
                            &lt;/a&gt;
                            &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Calendar&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Chat app&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Inbox&lt;/a&gt;&lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/li&gt;
                        &lt;li class=&quot;nav-item dropdown&quot;&gt;
                            &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                                &lt;i class=&quot;fa fa-file me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Pages&lt;/span&gt;
                            &lt;/a&gt;
                            &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;profile.html&quot;&gt;Profile&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;timeline.html&quot;&gt;Timeline&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;imagegallery.html&quot;&gt;Image Gallery&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;invoices.html&quot;&gt;Invoices&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;pricing.html&quot;&gt;Pricing&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;teamsboard.html&quot;&gt;Teams Board&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;faqs.html&quot;&gt;FAQs&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Widget's&lt;/a&gt;&lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/li&gt;
                        &lt;li class=&quot;nav-item dropdown&quot;&gt;
                            &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                                &lt;i class=&quot;fa fa-lock me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Authentication&lt;/span&gt;
                            &lt;/a&gt;
                            &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-signin.html&quot;&gt;Sign in&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-signup.html&quot;&gt;Sign up&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-password-reset&quot;&gt;Password reset&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-two-step&quot;&gt;2-Step Authentication&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;auth-404&quot;&gt;404&lt;/a&gt;&lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/li&gt;
                        &lt;li class=&quot;nav-item dropdown&quot;&gt;
                            &lt;a class=&quot;nav-link dropdown-toggle&quot; href=&quot;#&quot; role=&quot;button&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
                                &lt;i class=&quot;fa fa-file-text me-2&quot;&gt;&lt;/i&gt;&lt;span&gt;Docs&lt;/span&gt;
                            &lt;/a&gt;
                            &lt;ul class=&quot;dropdown-menu rounded-lg shadow border-0 dropdown-animation&quot;&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Stater page&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Documentation&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Changelog&lt;/a&gt;&lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

            &lt;/div&gt;
        &lt;/nav&gt;
    &lt;/div&gt;

&lt;/div&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border-top mt-5 pt-3">
                            <h3 id="external-content">External content</h3>
                            <p>Sometimes you want to use the collapse plugin to trigger hidden content elsewhere on the page. Because our plugin works on the <code>id</code> and <code>data-target</code> matching, that’s easily done!</p>
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview5" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML5" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview5" role="tabpanel">
                                            <div class="collapse" id="navbarToggleExternalContent">
                                                <div class="bg-dark p-4">
                                                    <h5 class="text-white h4">Collapsed content</h5>
                                                    <span class="text-muted">Toggleable via the navbar brand.</span>
                                                </div>
                                              </div>
                                              <nav class="navbar navbar-dark bg-dark">
                                                    <div class="container">
                                                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarToggleExternalContent" aria-controls="navbarToggleExternalContent" aria-expanded="false" aria-label="Toggle navigation">
                                                        <span class="navbar-toggler-icon"></span>
                                                    </button>
                                                </div>
                                            </nav>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML5" role="tabpanel">
<pre class="language-html m-0" data-lang="html">
<code>&lt;div class=&quot;collapse&quot; id=&quot;navbarToggleExternalContent&quot;&gt;
    &lt;div class=&quot;bg-dark p-4&quot;&gt;
        &lt;h5 class=&quot;text-white h4&quot;&gt;Collapsed content&lt;/h5&gt;
        &lt;span class=&quot;text-muted&quot;&gt;Toggleable via the navbar brand.&lt;/span&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;nav class=&quot;navbar navbar-dark bg-dark&quot;&gt;
        &lt;div class=&quot;container-fluid&quot;&gt;
        &lt;button class=&quot;navbar-toggler&quot; type=&quot;button&quot; data-toggle=&quot;collapse&quot; data-target=&quot;#navbarToggleExternalContent&quot; aria-controls=&quot;navbarToggleExternalContent&quot; aria-expanded=&quot;false&quot; aria-label=&quot;Toggle navigation&quot;&gt;
            &lt;span class=&quot;navbar-toggler-icon&quot;&gt;&lt;/span&gt;
        &lt;/button&gt;
    &lt;/div&gt;
&lt;/nav&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer">
            <div class="container">
                <div class="col-12">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row justify-content-between align-items-center">
                                <div class="col">
                                    <p class="mb-0">© AL-UI. <span class="d-none d-sm-inline-block">2021 ThemeMakker.</span></p>
                                </div>
                                <div class="col-auto">
                                    <div class="d-flex justify-content-end">
                                        <!-- List Dot -->
                                        <ul class="list-inline mb-0">
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/about/">About</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/hire-us/">Hire us</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/all-templates/">Template</a>
                                            </li>
                                
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://themeforest.net/licenses/standard" target="_blank">License</a>
                                            </li>
                                        </ul>
                                        <!-- End List Dot -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title">Ready to build Layouts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-c/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-e/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-k/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-k.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Body Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-p/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-p.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Background BG</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-m/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-m.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Header <span class="small">(Fixed)</span></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">AL-UI Setting</h5>
                    </div>
                    <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan" class="active"><div class="cyan"></div></li>
                            <li data-theme="green"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-1">
                                <input class="form-check-input" type="checkbox" id="CheckGradient">
                                <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                            </div>
                    </div>
                    <!-- Settings: bg image -->
                    <div class="setting-img mb-3">
                        <div class="form-check form-switch imagebg-switch mb-1">
                            <input class="form-check-input" type="checkbox" id="CheckImage">
                            <label class="form-check-label" for="CheckImage">Set Background Image (Sidebar)</label>
                        </div>
                        <div class="bg-images">
                            <ul class="list-unstyled d-flex justify-content-between">
                                <li class="sidebar-img-1 sidebar-img-active"><a class="rounded sidebar-img" id="img-1" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-1.jpg" alt="" /></a></li>
                                <li class="sidebar-img-2"><a class="rounded sidebar-img" id="img-2" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-2.jpg" alt="" /></a></li>
                                <li class="sidebar-img-3"><a class="rounded sidebar-img" id="img-3" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-3.jpg" alt="" /></a></li>
                                <li class="sidebar-img-4"><a class="rounded sidebar-img" id="img-4" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-4.jpg" alt="" /></a></li>
                                <li class="sidebar-img-5"><a class="rounded sidebar-img" id="img-5" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-5.jpg" alt="" /></a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>

<!-- Prism js file please do not add in your project -->
<script src="../../../assets/plugin/prismjs/prism.js"></script>

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
</body>
</html>