<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Hospital Management</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">
    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.d.sub.header.min.css">
    <!-- Prism css file please do not add in your project -->
    <link rel="stylesheet" href="../../../assets/plugin/prismjs/prism.css">
</head>

<body>

<div id="layout-d-sub-header" class="theme-cyan">

    <!-- Navigation -->
    <div class="header fixed-top shadow-sm">
        <nav class="navbar navbar-light bg-secondary py-2 py-md-3 px-lg-5 px-md-2">
            <div class="container">

                <!-- Brand -->
                <a href="index.html" class="me-3 me-lg-4 brand-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                        <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
                    </svg>
                </a>

                <!-- Search -->
                <div class="h-left d-none d-sm-block">
                    <div class="input-group border rounded">
                        <button class="btn btn-outline-secondary dropdown-toggle border-0 d-none d-sm-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">Fillter</button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Action</a></li>
                            <li><a class="dropdown-item" href="#">Another action</a></li>
                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Separated link</a></li>
                        </ul>
                        <input type="text" class="form-control bg-transparent border-0" placeholder="Search here...">
                    </div>
                </div>

                <!-- header rightbar icon -->
                <div class="h-right flex-grow-1 justify-content-end d-flex align-items-center me-5 me-lg-0">
                    <div class="d-flex">
                        <a class="nav-link text-primary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                        <a class="nav-link text-primary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                            <i class="fa fa-sliders"></i>
                        </a>
                    </div>
                    <div class="dropdown notifications">
                        <a class="nav-link dropdown-toggle pulse" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-bell"></i>
                            <span class="pulse-ring"></span>
                        </a>
                        <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w380">
                                <div class="card-header border-0 p-3">
                                    <h5 class="mb-0 fw-light d-flex justify-content-between">
                                        <span>Notifications Center</span>
                                        <span class="badge text-muted">14</span>
                                    </h5>
                                    <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tab-content card-body custom_scroll">
                                    <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Chris Morise</span> <small>2MIN</small></p>
                                                        <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Kelly</span> <small>1DAY</small></p>
                                                        <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">making it over 2000 years old</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">savera</span> <small>1DAY</small></p>
                                                        <span class="text-muted">The generated Lorem Ipsum</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                        <small class="text-muted">10:00 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                        <small class="text-muted">04:00 PM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                        <h4>No Logs right now!</h4>
                                    </div>
                                </div>
                                <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown user-profile ms-2 ms-sm-3">
                        <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                            <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                        </a>
                        <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w240">
                                <div class="card-body border-bottom">
                                    <div class="d-flex py-1">
                                        <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted"><span class="fw-bold">Chris Morise</span></p>
                                            <small class="text-muted"><EMAIL></small>
                                            <div>
                                                <a href="#" class="card-link">Sign out</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group m-2">
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile & account</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </nav>

        <!-- Sub header: menu -->
        <div class="sub-header">
            <nav class="navbar navbar-expand-lg p-0">
                <div class="container">

                    <!-- menu toggler -->
                    <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mainHeader">
                        <span class="fa fa-bars"></span>
                    </button>

                    <!-- main menu -->
                    <div class="collapse navbar-collapse order-0 py-1 py-md-2" id="mainHeader">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="index.html"><i class="fa fa-dashboard me-2"></i><span>Dashboard</span></a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-slack me-2"></i><span>Apps</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="calendar.html">Calendar</a></li>
                                    <li><a class="dropdown-item" href="chat.html">Chat app</a></li>
                                    <li><a class="dropdown-item" href="#">Inbox</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file me-2"></i><span>Pages</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="profile.html">Profile</a></li>
                                    <li><a class="dropdown-item" href="timeline.html">Timeline</a></li>
                                    <li><a class="dropdown-item" href="imagegallery.html">Image Gallery</a></li>
                                    <li><a class="dropdown-item" href="invoices.html">Invoices</a></li>
                                    <li><a class="dropdown-item" href="pricing.html">Pricing</a></li>
                                    <li><a class="dropdown-item" href="teamsboard.html">Teams Board</a></li>
                                    <li><a class="dropdown-item" href="faqs.html">FAQs</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/w-cards.html">Widget's</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-lock me-2"></i><span>Authentication</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="auth-signin.html">Sign in</a></li>
                                    <li><a class="dropdown-item" href="auth-signup.html">Sign up</a></li>
                                    <li><a class="dropdown-item" href="auth-password-reset.html">Password reset</a></li>
                                    <li><a class="dropdown-item" href="auth-two-step.html">2-Step Authentication</a></li>
                                    <li><a class="dropdown-item" href="auth-404.html">404</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-diamond me-2"></i><span>UI Components</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="ui-alerts.html">Alerts</a></li>
                                    <li><a class="dropdown-item" href="ui-badge.html">Badge</a></li>
                                    <li><a class="dropdown-item" href="ui-breadcrumb.html">Breadcrumb</a></li>
                                    <li><a class="dropdown-item active" href="ui-buttons.html">Buttons</a></li>
                                    <li><a class="dropdown-item" href="ui-card.html">Card</a></li>
                                    <li><a class="dropdown-item" href="ui-carousel.html">Carousel</a></li>
                                    <li><a class="dropdown-item" href="ui-collapse.html">Collapse</a></li>
                                    <li><a class="dropdown-item" href="ui-dropdowns.html">Dropdowns</a></li>
                                    <li><a class="dropdown-item" href="ui-listgroup.html">List group</a></li>
                                    <li><a class="dropdown-item" href="ui-modal.html">Modal</a></li>
                                    <li><a class="dropdown-item" href="ui-navs.html">Navs</a></li>
                                    <li><a class="dropdown-item" href="ui-navbar.html">Navbar</a></li>
                                    <li><a class="dropdown-item" href="ui-pagination.html">Pagination</a></li>
                                    <li><a class="dropdown-item" href="ui-popovers.html">Popovers</a></li>
                                    <li><a class="dropdown-item" href="ui-progress.html">Progress</a></li>
                                    <li><a class="dropdown-item" href="ui-scrollspy.html">Scrollspy</a></li>
                                    <li><a class="dropdown-item" href="ui-spinners.html">Spinners</a></li>
                                    <li><a class="dropdown-item" href="ui-toasts.html">Toasts</a></li>
                                    <li><a class="dropdown-item" href="ui-tooltips.html">Tooltips</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file-text me-2"></i><span>Docs</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="../../../documentation/stater-page.html">Stater page</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/index.html">Documentation</a></li>
                                    <li><a class="dropdown-item" href="../changelog.html">Changelog</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                </div>
            </nav>
        </div>
    </div>

    <!-- main body area -->
    <div class="main px-lg-5 px-md-2">

        <!-- Body: Header -->
        <div class="body-header border-bottom d-flex py-lg-3 py-md-2">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <ol class="breadcrumb d-inline-flex bg-transparent p-0 m-0">
                            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                            <li class="breadcrumb-item">Components</li>
                            <li class="breadcrumb-item active">Buttons</li>
                        </ol>
                        <h1 class="h4 mt-1">Buttons and Buttons groups</h1>
                    </div>
                    <div class="col-auto">
                        <a href="https://v5.getbootstrap.com/" class="btn btn-dark lift">GetBootstrap</a>
                    </div>
                </div>  <!-- Row end  -->
            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex py-lg-4 py-3">
            <div class="container">
                <div class="col-12">
                    <div class="card mb-4 shadow-sm border-0">
                        <div class="card-body">
                            <ul class="nav nav-tabs tab-body-header rounded d-inline-flex" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#btn-normal" role="tab">Buttons</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#btn-group" role="tab">Buttons Groups</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="btn-normal" role="tabpanel">
                            <div class="row justify-content-between">
                                <div class="col-lg-8 col-sm-12">

                                    <h2 id="examples">Examples</h2>
                                    <p>Bootstrap includes several predefined button styles, each serving its own semantic purpose, with a few extras thrown in for more control.</p>
                                    <div class="bd-example mb-5">
                                        <button type="button" class="btn btn-primary">Primary</button>
                                        <button type="button" class="btn btn-secondary">Secondary</button>
                                        <button type="button" class="btn btn-success">Success</button>
                                        <button type="button" class="btn btn-danger">Danger</button>
                                        <button type="button" class="btn btn-warning">Warning</button>
                                        <button type="button" class="btn btn-info">Info</button>
                                        <button type="button" class="btn btn-light">Light</button>
                                        <button type="button" class="btn btn-dark">Dark</button>
                                        
                                        <button type="button" class="btn btn-link">Link</button>
<pre>
<code class="language-html" data-lang="html">&lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Primary&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-secondary&quot;&gt;Secondary&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-success&quot;&gt;Success&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-danger&quot;&gt;Danger&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-warning&quot;&gt;Warning&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-info&quot;&gt;Info&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-light&quot;&gt;Light&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-dark&quot;&gt;Dark&lt;/button&gt;

&lt;button type=&quot;button&quot; class=&quot;btn btn-link&quot;&gt;Link&lt;/button&gt;</code>
</pre>
                                    </div>
                                    
                                    <div class="bd-callout bd-callout-info">
                                        <h5 id="conveying-meaning-to-assistive-technologies">Conveying meaning to assistive technologies</h5>
                                        <p>Using color to add meaning only provides a visual indication, which will not be conveyed to users of assistive technologies – such as screen readers. Ensure that information denoted by the color is either obvious from the content itself (e.g. the visible text), or is included through alternative means, such as additional text hidden with the <code>.visually-hidden</code> class.</p>
                                    </div>
                                    
                                    <h2 id="disable-text-wrapping">Disable text wrapping</h2>
                                    <p>If you don’t want the button text to wrap, you can add the <code>.text-nowrap</code> class to the button. In Sass, you can set <code>$btn-white-space: nowrap</code> to disable text wrapping for each button.</p>
                                    <h2 id="button-tags">Button tags<a class="anchorjs-link " aria-label="Anchor" data-anchorjs-icon="#" href="#button-tags" style="padding-left: 0.375em;"></a></h2>
                                    <p>The <code>.btn</code> classes are designed to be used with the <code>&lt;button&gt;</code> element. However, you can also use these classes on <code>&lt;a&gt;</code> or <code>&lt;input&gt;</code> elements (though some browsers may apply a slightly different rendering).</p>
                                    <p>When using button classes on <code>&lt;a&gt;</code> elements that are used to trigger in-page functionality (like collapsing content), rather than linking to new pages or sections within the current page, these links should be given a <code>role="button"</code> to appropriately convey their purpose to assistive technologies such as screen readers.</p>
                                    <div class="bd-example mb-5">
                                        <a class="btn btn-primary" href="#" role="button">Link</a>
                                        <button class="btn btn-primary" type="submit">Button</button>
                                        <input class="btn btn-primary" type="button" value="Input">
                                        <input class="btn btn-primary" type="submit" value="Submit">
                                        <input class="btn btn-primary" type="reset" value="Reset">
<pre>
<code class="language-html" data-lang="html">&lt;a class=&quot;btn btn-primary&quot; href=&quot;#&quot; role=&quot;button&quot;&gt;Link&lt;/a&gt;
&lt;button class=&quot;btn btn-primary&quot; type=&quot;submit&quot;&gt;Button&lt;/button&gt;
&lt;input class=&quot;btn btn-primary&quot; type=&quot;button&quot; value=&quot;Input&quot;&gt;
&lt;input class=&quot;btn btn-primary&quot; type=&quot;submit&quot; value=&quot;Submit&quot;&gt;
&lt;input class=&quot;btn btn-primary&quot; type=&quot;reset&quot; value=&quot;Reset&quot;&gt;</code>
</pre>
                                    </div>


                                    <h2 id="outline-buttons">Outline buttons</h2>
                                    <p>In need of a button, but not the hefty background colors they bring? Replace the default modifier classes with the <code>.btn-outline-*</code> ones to remove all background images and colors on any button.</p>
                                    <div class="bd-example mb-5">
                                    
                                        <button type="button" class="btn btn-outline-primary">Primary</button>
                                        <button type="button" class="btn btn-outline-secondary">Secondary</button>
                                        <button type="button" class="btn btn-outline-success">Success</button>
                                        <button type="button" class="btn btn-outline-danger">Danger</button>
                                        <button type="button" class="btn btn-outline-warning">Warning</button>
                                        <button type="button" class="btn btn-outline-info">Info</button>
                                        <button type="button" class="btn btn-outline-light">Light</button>
                                        <button type="button" class="btn btn-outline-dark">Dark</button>

<pre>
<code class="language-html" data-lang="html">&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-primary&quot;&gt;Primary&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;Secondary&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-success&quot;&gt;Success&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-danger&quot;&gt;Danger&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-warning&quot;&gt;Warning&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-info&quot;&gt;Info&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-light&quot;&gt;Light&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Dark&lt;/button&gt;</code>
</pre>
                                    </div>

                                    <h2 id="sizes">Sizes</h2>
                                    <p>Fancy larger or smaller buttons? Add <code>.btn-lg</code> or <code>.btn-sm</code> for additional sizes.</p>
                                    <div class="bd-example mb-2">
                                        <button type="button" class="btn btn-primary btn-lg">Large button</button>
                                        <button type="button" class="btn btn-secondary btn-lg">Large button</button>
<pre>
<code class="language-html" data-lang="html">&lt;button type=&quot;button&quot; class=&quot;btn btn-primary btn-lg&quot;&gt;Large button&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-secondary btn-lg&quot;&gt;Large button&lt;/button&gt;</code>
</pre>
                                    </div>                            
                                    <div class="bd-example mb-5">
                                        <button type="button" class="btn btn-primary btn-sm">Small button</button>
                                        <button type="button" class="btn btn-secondary btn-sm">Small button</button>
<pre>
<code class="language-html" data-lang="html">&lt;button type=&quot;button&quot; class=&quot;btn btn-primary btn-sm&quot;&gt;Small button&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-secondary btn-sm&quot;&gt;Small button&lt;/button&gt;</code>
</pre>
                                    </div>
                                    
                                    <p>Create block level buttons—those that span the full width of a parent—by adding <code>.btn-block</code>.</p>
                                    <div class="bd-example mb-5">
                                        <button type="button" class="btn btn-primary btn-lg btn-block">Block level button</button>
                                        <button type="button" class="btn btn-secondary btn-lg btn-block">Block level button</button>
<pre>
<code class="language-html" data-lang="html">&lt;button type=&quot;button&quot; class=&quot;btn btn-primary btn-lg btn-block&quot;&gt;Block level button&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-secondary btn-lg btn-block&quot;&gt;Block level button&lt;/button&gt;</code>
</pre>
                                    </div>
                                    
                                    <h2 id="disabled-state">Disabled state</h2>
                                    <p>Make buttons look inactive by adding the <code>disabled</code> boolean attribute to any <code>&lt;button&gt;</code> element. Disabled buttons have <code>pointer-events: none</code> applied to, preventing hover and active states from triggering.</p>
                                    <div class="bd-example mb-5">
                                        <button type="button" class="btn btn-lg btn-primary" disabled="">Primary button</button>
                                        <button type="button" class="btn btn-secondary btn-lg" disabled="">Button</button>
<pre>
<code class="language-html" data-lang="html">&lt;button type=&quot;button&quot; class=&quot;btn btn-lg btn-primary&quot; disabled=&quot;&quot;&gt;Primary button&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-secondary btn-lg&quot; disabled=&quot;&quot;&gt;Button&lt;/button&gt;</code>
</pre>
                                    </div>
                                    
                                    <p>Disabled buttons using the <code>&lt;a&gt;</code> element behave a bit different:</p>
                                    <ul>
                                        <li><code>&lt;a&gt;</code>s don’t support the <code>disabled</code> attribute, so you must add the <code>.disabled</code> class to make it visually appear disabled.</li>
                                        <li>Some future-friendly styles are included to disable all <code>pointer-events</code> on anchor buttons.</li>
                                        <li>Disabled buttons should include the <code>aria-disabled="true"</code> attribute to indicate the state of the element to assistive technologies.</li>
                                    </ul>
                                    <div class="bd-example mb-5">
                                        <a href="#" class="btn btn-primary btn-lg disabled" tabindex="-1" role="button" aria-disabled="true">Primary link</a>
                                        <a href="#" class="btn btn-secondary btn-lg disabled" tabindex="-1" role="button" aria-disabled="true">Link</a>
<pre>
<code class="language-html" data-lang="html">&lt;a href=&quot;#&quot; class=&quot;btn btn-primary btn-lg disabled&quot; tabindex=&quot;-1&quot; role=&quot;button&quot; aria-disabled=&quot;true&quot;&gt;Primary link&lt;/a&gt;
&lt;a href=&quot;#&quot; class=&quot;btn btn-secondary btn-lg disabled&quot; tabindex=&quot;-1&quot; role=&quot;button&quot; aria-disabled=&quot;true&quot;&gt;Link&lt;/a&gt;</code>
</pre>
                                    </div>

                                    
                                    <div class="bd-callout bd-callout-warning">
                                        <h5 id="link-functionality-caveat">Link functionality caveat</h5>
                                        <p>The <code>.disabled</code> class uses <code>pointer-events: none</code> to try to disable the link functionality of <code>&lt;a&gt;</code>s, but that CSS property is not yet standardized. In addition, even in browsers that do support <code>pointer-events: none</code>, keyboard navigation remains unaffected, meaning that sighted keyboard users and users of assistive technologies will still be able to activate these links. So to be safe, in addition to <code>aria-disabled="true"</code>, also include a <code>tabindex="-1"</code> attribute on these links to prevent them from receiving keyboard focus, and use custom JavaScript to disable their functionality altogether.</p>
                                    </div>
                                    
                                    <h2 id="button-plugin">Button plugin</h2>
                                    <p>The button plugin allows you to create simple on/off toggle buttons.</p>
                                    <div class="card p-4 mb-5 shadow-sm">
                                        Visually, these toggle buttons are identical to the <a href="https://v5.getbootstrap.com/docs/5.0/forms/checks-radios/#checkbox-toggle-buttons">checkbox toggle buttons</a>. However, they are conveyed differently by assistive technologies: the checkbox toggles will be announced by screen readers as “checked”/“not checked” (since, despite their appearance, they are fundamentally still checkboxes), whereas these toggle buttons will be announced as “button”/“button pressed”. The choice between these two approaches will depend on the type of toggle you are creating, and whether or not the toggle will make sense to users when announced as a checkbox or as an actual button.
                                    </div>
                                    
                                    <h3 id="toggle-states">Toggle states</h3>
                                    <p>Add <code>data-bs-toggle="button"</code> to toggle a button’s <code>active</code> state. If you’re pre-toggling a button, you must manually add the <code>.active</code> class <strong>and</strong> <code>aria-pressed="true"</code> to ensure that it is conveyed appropriately to assistive technologies.</p>
                                    <div class="bd-example mb-5">
                                        <button type="button" class="btn btn-primary" data-bs-toggle="button" autocomplete="off">Toggle button</button>
                                        <button type="button" class="btn btn-primary active" data-bs-toggle="button" autocomplete="off" aria-pressed="true">Active toggle button</button>
                                        <button type="button" class="btn btn-primary" disabled="" data-bs-toggle="button" autocomplete="off">Disabled toggle button</button>
<pre>
<code class="language-html" data-lang="html">&lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot; data-toggle=&quot;button&quot; autocomplete=&quot;off&quot;&gt;Toggle button&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-primary active&quot; data-toggle=&quot;button&quot; autocomplete=&quot;off&quot; aria-pressed=&quot;true&quot;&gt;Active toggle button&lt;/button&gt;
&lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot; disabled=&quot;&quot; data-toggle=&quot;button&quot; autocomplete=&quot;off&quot;&gt;Disabled toggle button&lt;/button&gt;</code>
</pre>
                                    </div>
                                    
                                    <div class="bd-example mb-5">
                                        <a href="#" class="btn btn-primary" role="button" data-bs-toggle="button">Toggle link</a>
                                        <a href="#" class="btn btn-primary active" role="button" data-bs-toggle="button" aria-pressed="true">Active toggle link</a>
                                        <a href="#" class="btn btn-primary disabled" tabindex="-1" aria-disabled="true" role="button" data-bs-toggle="button">Disabled toggle link</a>
<pre>
<code class="language-html" data-lang="html">&lt;a href=&quot;#&quot; class=&quot;btn btn-primary&quot; role=&quot;button&quot; data-toggle=&quot;button&quot;&gt;Toggle link&lt;/a&gt;
&lt;a href=&quot;#&quot; class=&quot;btn btn-primary active&quot; role=&quot;button&quot; data-toggle=&quot;button&quot; aria-pressed=&quot;true&quot;&gt;Active toggle link&lt;/a&gt;
&lt;a href=&quot;#&quot; class=&quot;btn btn-primary disabled&quot; tabindex=&quot;-1&quot; aria-disabled=&quot;true&quot; role=&quot;button&quot; data-toggle=&quot;button&quot;&gt;Disabled toggle link&lt;/a&gt;</code>
</pre>
                                    </div>


                                    <h3 id="methods">Methods</h3>
                                    <p>You can create a button instance with the button constructor, for example:</p>
                                    <div class="bd-example mb-5">
<pre><code class="language-js" data-lang="js"><span class="kd">var</span> <span class="nx">button</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nx">getElementById</span><span class="p">(</span><span class="s1">'myButton'</span><span class="p">)</span>
<span class="kd">var</span> <span class="nx">bsButton</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">bootstrap</span><span class="p">.</span><span class="nx">Button</span><span class="p">(</span><span class="nx">button</span><span class="p">)</span>
</code></pre>
                                    </div>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Method</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>toggle</code></td>
                                                <td>Toggles push state. Gives the button the appearance that it has been activated.</td>
                                            </tr>
                                            <tr>
                                                <td><code>dispose</code></td>
                                                <td>Destroys an element's button.</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <p>For example, to toggle all buttons</p>
                                    <div class="bd-example mb-5">
<pre><code class="language-js" data-lang="js"><span class="kd">var</span> <span class="nx">buttons</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nx">querySelectorAll</span><span class="p">(</span><span class="s1">'.btn'</span><span class="p">)</span>
<span class="nx">buttons</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="kd">function</span> <span class="p">(</span><span class="nx">button</span><span class="p">)</span> <span class="p">{</span>
    <span class="kd">var</span> <span class="nx">button</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">bootstrap</span><span class="p">.</span><span class="nx">Button</span><span class="p">(</span><span class="nx">button</span><span class="p">)</span>
    <span class="nx">button</span><span class="p">.</span><span class="nx">toggle</span><span class="p">()</span>
<span class="p">})</span>
</code></pre>
                                    </div>

                                </div>
                                <div class="col-lg-3 col-sm-12 d-none d-sm-block">
                                    <div class="sticky-lg-top">
                                        <strong class="d-block h6 my-2 pb-2 border-bottom">On this page</strong>
                                        <nav>
                                            <ul>
                                                <li><a href="#examples">Examples</a></li>
                                                <li><a href="#disable-text-wrapping">Disable text wrapping</a></li>
                                                <li><a href="#button-tags">Button tags</a></li>
                                                <li><a href="#outline-buttons">Outline buttons</a></li>
                                                <li><a href="#sizes">Sizes</a></li>
                                                <li><a href="#disabled-state">Disabled state</a></li>
                                                <li><a href="#button-plugin">Button plugin</a>
                                                    <ul>
                                                        <li><a href="#toggle-states">Toggle states</a></li>
                                                        <li><a href="#methods">Methods</a></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div> <!-- Row end  -->
                        </div>

                        <div class="tab-pane fade" id="btn-group" role="tabpanel">
                            <div class="row justify-content-between">
                                <div class="col-lg-8 col-sm-12">
                                    <h2 id="basic-example">Basic example</h2>
                                    <p>Wrap a series of buttons with <code>.btn</code> in <code>.btn-group</code>.</p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group" role="group" aria-label="Basic example">
                                            <button type="button" class="btn btn-primary">Left</button>
                                            <button type="button" class="btn btn-primary">Middle</button>
                                            <button type="button" class="btn btn-primary">Right</button>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Basic example&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Left&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Middle&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Right&lt;/button&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <div class="bd-callout bd-callout-warning">
                                        <h5 id="ensure-correct-role-and-provide-a-label">Ensure correct <code>role</code> and provide a label</h5>
                                        <p>In order for assistive technologies (such as screen readers) to convey that a series of buttons is grouped, an appropriate <code>role</code> attribute needs to be provided. For button groups, this would be <code>role="group"</code>, while toolbars should have a <code>role="toolbar"</code>.</p>
                                        <p>In addition, groups and toolbars should be given an explicit label, as most assistive technologies will otherwise not announce them, despite the presence of the correct role attribute. In the examples provided here, we use <code>aria-label</code>, but alternatives such as <code>aria-labelledby</code> can also be used.</p>
                                    </div>
                                    
                                    <p>These classes can also be added to groups of links, as an alternative to the <a href="/docs/5.0/components/navs/"><code>.nav</code> navigation components</a>.</p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group">
                                            <a href="#" class="btn btn-primary active" aria-current="page">Active link</a>
                                            <a href="#" class="btn btn-primary">Link</a>
                                            <a href="#" class="btn btn-primary">Link</a>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group&quot;&gt;
    &lt;a href=&quot;#&quot; class=&quot;btn btn-primary active&quot; aria-current=&quot;page&quot;&gt;Active link&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;btn btn-primary&quot;&gt;Link&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;btn btn-primary&quot;&gt;Link&lt;/a&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <h2 id="mixed-styles">Mixed styles</h2>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group" role="group" aria-label="Basic mixed styles example">
                                            <button type="button" class="btn btn-danger">Left</button>
                                            <button type="button" class="btn btn-warning">Middle</button>
                                            <button type="button" class="btn btn-success">Right</button>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Basic mixed styles example&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-danger&quot;&gt;Left&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-warning&quot;&gt;Middle&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-success&quot;&gt;Right&lt;/button&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <h2 id="outlined-styles">Outlined styles</h2>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group" role="group" aria-label="Basic outlined example">
                                            <button type="button" class="btn btn-outline-primary">Left</button>
                                            <button type="button" class="btn btn-outline-primary">Middle</button>
                                            <button type="button" class="btn btn-outline-primary">Right</button>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Basic outlined example&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-primary&quot;&gt;Left&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-primary&quot;&gt;Middle&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-primary&quot;&gt;Right&lt;/button&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <h2 id="checkbox-and-radio-button-groups">Checkbox and radio button groups</h2>
                                    <p>Combine button-like checkbox and radio <a href="/docs/5.0/forms/checks-radios/">toggle buttons</a> into a seamless looking button group.</p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group" role="group" aria-label="Basic checkbox toggle button group">
                                            <input type="checkbox" class="btn-check" id="btncheck1" autocomplete="off">
                                            <label class="btn btn-outline-primary" for="btncheck1">Checkbox 1</label>
                                        
                                            <input type="checkbox" class="btn-check" id="btncheck2" autocomplete="off">
                                            <label class="btn btn-outline-primary" for="btncheck2">Checkbox 2</label>
                                        
                                            <input type="checkbox" class="btn-check" id="btncheck3" autocomplete="off">
                                            <label class="btn btn-outline-primary" for="btncheck3">Checkbox 3</label>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Basic checkbox toggle button group&quot;&gt;
    &lt;input type=&quot;checkbox&quot; class=&quot;btn-check&quot; id=&quot;btncheck1&quot; autocomplete=&quot;off&quot;&gt;
    &lt;label class=&quot;btn btn-outline-primary&quot; for=&quot;btncheck1&quot;&gt;Checkbox 1&lt;/label&gt;

    &lt;input type=&quot;checkbox&quot; class=&quot;btn-check&quot; id=&quot;btncheck2&quot; autocomplete=&quot;off&quot;&gt;
    &lt;label class=&quot;btn btn-outline-primary&quot; for=&quot;btncheck2&quot;&gt;Checkbox 2&lt;/label&gt;

    &lt;input type=&quot;checkbox&quot; class=&quot;btn-check&quot; id=&quot;btncheck3&quot; autocomplete=&quot;off&quot;&gt;
    &lt;label class=&quot;btn btn-outline-primary&quot; for=&quot;btncheck3&quot;&gt;Checkbox 3&lt;/label&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>                                    
                                    <div class="bd-example mb-5">
                                        <div class="btn-group" role="group" aria-label="Basic radio toggle button group">
                                            <input type="radio" class="btn-check" name="btnradio" id="btnradio1" autocomplete="off" checked="">
                                            <label class="btn btn-outline-primary" for="btnradio1">Radio 1</label>
                                        
                                            <input type="radio" class="btn-check" name="btnradio" id="btnradio2" autocomplete="off">
                                            <label class="btn btn-outline-primary" for="btnradio2">Radio 2</label>
                                        
                                            <input type="radio" class="btn-check" name="btnradio" id="btnradio3" autocomplete="off">
                                            <label class="btn btn-outline-primary" for="btnradio3">Radio 3</label>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Basic radio toggle button group&quot;&gt;
    &lt;input type=&quot;radio&quot; class=&quot;btn-check&quot; name=&quot;btnradio&quot; id=&quot;btnradio1&quot; autocomplete=&quot;off&quot; checked=&quot;&quot;&gt;
    &lt;label class=&quot;btn btn-outline-primary&quot; for=&quot;btnradio1&quot;&gt;Radio 1&lt;/label&gt;

    &lt;input type=&quot;radio&quot; class=&quot;btn-check&quot; name=&quot;btnradio&quot; id=&quot;btnradio2&quot; autocomplete=&quot;off&quot;&gt;
    &lt;label class=&quot;btn btn-outline-primary&quot; for=&quot;btnradio2&quot;&gt;Radio 2&lt;/label&gt;

    &lt;input type=&quot;radio&quot; class=&quot;btn-check&quot; name=&quot;btnradio&quot; id=&quot;btnradio3&quot; autocomplete=&quot;off&quot;&gt;
    &lt;label class=&quot;btn btn-outline-primary&quot; for=&quot;btnradio3&quot;&gt;Radio 3&lt;/label&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>                                    
                                    
                                    <h2 id="button-toolbar">Button toolbar</h2>
                                    <p>Combine sets of button groups into button toolbars for more complex components. Use utility classes as needed to space out groups, buttons, and more.</p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-toolbar" role="toolbar" aria-label="Toolbar with button groups">
                                            <div class="btn-group me-2" role="group" aria-label="First group">
                                                <button type="button" class="btn btn-primary">1</button>
                                                <button type="button" class="btn btn-primary">2</button>
                                                <button type="button" class="btn btn-primary">3</button>
                                                <button type="button" class="btn btn-primary">4</button>
                                            </div>
                                            <div class="btn-group me-2" role="group" aria-label="Second group">
                                                <button type="button" class="btn btn-secondary">5</button>
                                                <button type="button" class="btn btn-secondary">6</button>
                                                <button type="button" class="btn btn-secondary">7</button>
                                            </div>
                                            <div class="btn-group" role="group" aria-label="Third group">
                                                <button type="button" class="btn btn-info">8</button>
                                            </div>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-toolbar&quot; role=&quot;toolbar&quot; aria-label=&quot;Toolbar with button groups&quot;&gt;
    &lt;div class=&quot;btn-group me-2&quot; role=&quot;group&quot; aria-label=&quot;First group&quot;&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;1&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;2&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;3&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;4&lt;/button&gt;
    &lt;/div&gt;
    &lt;div class=&quot;btn-group me-2&quot; role=&quot;group&quot; aria-label=&quot;Second group&quot;&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-secondary&quot;&gt;5&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-secondary&quot;&gt;6&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-secondary&quot;&gt;7&lt;/button&gt;
    &lt;/div&gt;
    &lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Third group&quot;&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-info&quot;&gt;8&lt;/button&gt;
    &lt;/div&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <p>Feel free to mix input groups with button groups in your toolbars. Similar to the example above, you’ll likely need some utilities though to space things properly.</p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-toolbar mb-3" role="toolbar" aria-label="Toolbar with button groups">
                                            <div class="btn-group me-2" role="group" aria-label="First group">
                                                <button type="button" class="btn btn-outline-secondary">1</button>
                                                <button type="button" class="btn btn-outline-secondary">2</button>
                                                <button type="button" class="btn btn-outline-secondary">3</button>
                                                <button type="button" class="btn btn-outline-secondary">4</button>
                                            </div>
                                            <div class="input-group">
                                                <div class="input-group-text" id="btnGroupAddon">@</div>
                                                <input type="text" class="form-control" placeholder="Input group example" aria-label="Input group example" aria-describedby="btnGroupAddon">
                                            </div>
                                        </div>
                                        
                                        <div class="btn-toolbar justify-content-between" role="toolbar" aria-label="Toolbar with button groups">
                                            <div class="btn-group" role="group" aria-label="First group">
                                                <button type="button" class="btn btn-outline-secondary">1</button>
                                                <button type="button" class="btn btn-outline-secondary">2</button>
                                                <button type="button" class="btn btn-outline-secondary">3</button>
                                                <button type="button" class="btn btn-outline-secondary">4</button>
                                            </div>
                                                <div class="input-group">
                                                <div class="input-group-text" id="btnGroupAddon2">@</div>
                                                <input type="text" class="form-control" placeholder="Input group example" aria-label="Input group example" aria-describedby="btnGroupAddon2">
                                            </div>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-toolbar mb-3&quot; role=&quot;toolbar&quot; aria-label=&quot;Toolbar with button groups&quot;&gt;
    &lt;div class=&quot;btn-group me-2&quot; role=&quot;group&quot; aria-label=&quot;First group&quot;&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;1&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;2&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;3&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;4&lt;/button&gt;
    &lt;/div&gt;
    &lt;div class=&quot;input-group&quot;&gt;
        &lt;div class=&quot;input-group-text&quot; id=&quot;btnGroupAddon&quot;&gt;@&lt;/div&gt;
        &lt;input type=&quot;text&quot; class=&quot;form-control&quot; placeholder=&quot;Input group example&quot; aria-label=&quot;Input group example&quot; aria-describedby=&quot;btnGroupAddon&quot;&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;div class=&quot;btn-toolbar justify-content-between&quot; role=&quot;toolbar&quot; aria-label=&quot;Toolbar with button groups&quot;&gt;
    &lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;First group&quot;&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;1&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;2&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;3&lt;/button&gt;
        &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-secondary&quot;&gt;4&lt;/button&gt;
    &lt;/div&gt;
        &lt;div class=&quot;input-group&quot;&gt;
        &lt;div class=&quot;input-group-text&quot; id=&quot;btnGroupAddon2&quot;&gt;@&lt;/div&gt;
        &lt;input type=&quot;text&quot; class=&quot;form-control&quot; placeholder=&quot;Input group example&quot; aria-label=&quot;Input group example&quot; aria-describedby=&quot;btnGroupAddon2&quot;&gt;
    &lt;/div&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <h2 id="sizing">Sizing</h2>
                                    <p>Instead of applying button sizing classes to every button in a group, just add <code>.btn-group-*</code> to each <code>.btn-group</code>, including each one when nesting multiple groups.</p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group btn-group-lg" role="group" aria-label="Large button group">
                                            <button type="button" class="btn btn-outline-dark">Left</button>
                                            <button type="button" class="btn btn-outline-dark">Middle</button>
                                            <button type="button" class="btn btn-outline-dark">Right</button>
                                        </div>
                                        <div class="mt-2"></div>
                                        <div class="btn-group" role="group" aria-label="Default button group">
                                            <button type="button" class="btn btn-outline-dark">Left</button>
                                            <button type="button" class="btn btn-outline-dark">Middle</button>
                                            <button type="button" class="btn btn-outline-dark">Right</button>
                                        </div>
                                        <div class="mt-2"></div>
                                        <div class="btn-group btn-group-sm" role="group" aria-label="Small button group">
                                            <button type="button" class="btn btn-outline-dark">Left</button>
                                            <button type="button" class="btn btn-outline-dark">Middle</button>
                                            <button type="button" class="btn btn-outline-dark">Right</button>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group btn-group-lg&quot; role=&quot;group&quot; aria-label=&quot;Large button group&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Left&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Middle&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Right&lt;/button&gt;
&lt;/div&gt;
&lt;br&gt;
&lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Default button group&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Left&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Middle&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Right&lt;/button&gt;
&lt;/div&gt;
&lt;br&gt;
&lt;div class=&quot;btn-group btn-group-sm&quot; role=&quot;group&quot; aria-label=&quot;Small button group&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Left&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Middle&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-outline-dark&quot;&gt;Right&lt;/button&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <h2 id="nesting">Nesting</h2>
                                    <p>Place a <code>.btn-group</code> within another <code>.btn-group</code> when you want dropdown menus mixed with a series of buttons.</p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group" role="group" aria-label="Button group with nested dropdown">
                                            <button type="button" class="btn btn-primary">1</button>
                                            <button type="button" class="btn btn-primary">2</button>
                                        
                                            <div class="btn-group" role="group">
                                                <button id="btnGroupDrop1" type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Dropdown
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                </ul>
                                            </div>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group&quot; role=&quot;group&quot; aria-label=&quot;Button group with nested dropdown&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;1&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;2&lt;/button&gt;

    &lt;div class=&quot;btn-group&quot; role=&quot;group&quot;&gt;
        &lt;button id=&quot;btnGroupDrop1&quot; type=&quot;button&quot; class=&quot;btn btn-primary dropdown-toggle&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
            Dropdown
        &lt;/button&gt;
        &lt;ul class=&quot;dropdown-menu&quot; aria-labelledby=&quot;btnGroupDrop1&quot;&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/div&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    
                                    <h2 id="vertical-variation">Vertical variation<a class="anchorjs-link " aria-label="Anchor" data-anchorjs-icon="#" href="#vertical-variation" style="padding-left: 0.375em;"></a></h2>
                                    <p>Make a set of buttons appear vertically stacked rather than horizontally. <strong>Split button dropdowns are not supported here.</strong></p>
                                    <div class="bd-example mb-5">
                                        <div class="btn-group-vertical" role="group" aria-label="Vertical button group">
                                            <button type="button" class="btn btn-dark">Button</button>
                                            <button type="button" class="btn btn-dark">Button</button>
                                            <button type="button" class="btn btn-dark">Button</button>
                                            <button type="button" class="btn btn-dark">Button</button>
                                            <button type="button" class="btn btn-dark">Button</button>
                                            <button type="button" class="btn btn-dark">Button</button>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group-vertical&quot; role=&quot;group&quot; aria-label=&quot;Vertical button group&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-dark&quot;&gt;Button&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-dark&quot;&gt;Button&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-dark&quot;&gt;Button&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-dark&quot;&gt;Button&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-dark&quot;&gt;Button&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-dark&quot;&gt;Button&lt;/button&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>

                                    <div class="bd-example mb-5">
                                        <div class="btn-group-vertical" role="group" aria-label="Vertical button group">
                                            <button type="button" class="btn btn-primary">Button</button>
                                            <button type="button" class="btn btn-primary">Button</button>
                                            <div class="btn-group" role="group">
                                                <button id="btnGroupVerticalDrop1" type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Dropdown
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="btnGroupVerticalDrop1">
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                </ul>
                                            </div>
                                            <button type="button" class="btn btn-primary">Button</button>
                                            <button type="button" class="btn btn-primary">Button</button>
                                            <div class="btn-group" role="group">
                                                <button id="btnGroupVerticalDrop2" type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Dropdown
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="btnGroupVerticalDrop2">
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                </ul>
                                            </div>
                                            <div class="btn-group" role="group">
                                                <button id="btnGroupVerticalDrop3" type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Dropdown
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="btnGroupVerticalDrop3">
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                </ul>
                                            </div>
                                            <div class="btn-group" role="group">
                                                <button id="btnGroupVerticalDrop4" type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Dropdown
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="btnGroupVerticalDrop4">
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                    <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                                                </ul>
                                            </div>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group-vertical&quot; role=&quot;group&quot; aria-label=&quot;Vertical button group&quot;&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Button&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Button&lt;/button&gt;
    &lt;div class=&quot;btn-group&quot; role=&quot;group&quot;&gt;
        &lt;button id=&quot;btnGroupVerticalDrop1&quot; type=&quot;button&quot; class=&quot;btn btn-primary dropdown-toggle&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
            Dropdown
        &lt;/button&gt;
        &lt;ul class=&quot;dropdown-menu&quot; aria-labelledby=&quot;btnGroupVerticalDrop1&quot;&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/div&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Button&lt;/button&gt;
    &lt;button type=&quot;button&quot; class=&quot;btn btn-primary&quot;&gt;Button&lt;/button&gt;
    &lt;div class=&quot;btn-group&quot; role=&quot;group&quot;&gt;
        &lt;button id=&quot;btnGroupVerticalDrop2&quot; type=&quot;button&quot; class=&quot;btn btn-primary dropdown-toggle&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
            Dropdown
        &lt;/button&gt;
        &lt;ul class=&quot;dropdown-menu&quot; aria-labelledby=&quot;btnGroupVerticalDrop2&quot;&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/div&gt;
    &lt;div class=&quot;btn-group&quot; role=&quot;group&quot;&gt;
        &lt;button id=&quot;btnGroupVerticalDrop3&quot; type=&quot;button&quot; class=&quot;btn btn-primary dropdown-toggle&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
            Dropdown
        &lt;/button&gt;
        &lt;ul class=&quot;dropdown-menu&quot; aria-labelledby=&quot;btnGroupVerticalDrop3&quot;&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/div&gt;
    &lt;div class=&quot;btn-group&quot; role=&quot;group&quot;&gt;
        &lt;button id=&quot;btnGroupVerticalDrop4&quot; type=&quot;button&quot; class=&quot;btn btn-primary dropdown-toggle&quot; data-toggle=&quot;dropdown&quot; aria-expanded=&quot;false&quot;&gt;
            Dropdown
        &lt;/button&gt;
        &lt;ul class=&quot;dropdown-menu&quot; aria-labelledby=&quot;btnGroupVerticalDrop4&quot;&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a class=&quot;dropdown-item&quot; href=&quot;#&quot;&gt;Dropdown link&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/div&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>
                                    <div class="bd-example">
                                        <div class="btn-group-vertical" role="group" aria-label="Vertical radio toggle button group">
                                            <input type="radio" class="btn-check" name="vbtn-radio" id="vbtn-radio1" autocomplete="off" checked="">
                                            <label class="btn btn-outline-danger" for="vbtn-radio1">Radio 1</label>
                                            <input type="radio" class="btn-check" name="vbtn-radio" id="vbtn-radio2" autocomplete="off">
                                            <label class="btn btn-outline-danger" for="vbtn-radio2">Radio 2</label>
                                            <input type="radio" class="btn-check" name="vbtn-radio" id="vbtn-radio3" autocomplete="off">
                                            <label class="btn btn-outline-danger" for="vbtn-radio3">Radio 3</label>
                                        </div>
<pre>
<code class="language-html" data-lang="html">&lt;div class=&quot;btn-group-vertical&quot; role=&quot;group&quot; aria-label=&quot;Vertical radio toggle button group&quot;&gt;
    &lt;input type=&quot;radio&quot; class=&quot;btn-check&quot; name=&quot;vbtn-radio&quot; id=&quot;vbtn-radio1&quot; autocomplete=&quot;off&quot; checked=&quot;&quot;&gt;
    &lt;label class=&quot;btn btn-outline-danger&quot; for=&quot;vbtn-radio1&quot;&gt;Radio 1&lt;/label&gt;
    &lt;input type=&quot;radio&quot; class=&quot;btn-check&quot; name=&quot;vbtn-radio&quot; id=&quot;vbtn-radio2&quot; autocomplete=&quot;off&quot;&gt;
    &lt;label class=&quot;btn btn-outline-danger&quot; for=&quot;vbtn-radio2&quot;&gt;Radio 2&lt;/label&gt;
    &lt;input type=&quot;radio&quot; class=&quot;btn-check&quot; name=&quot;vbtn-radio&quot; id=&quot;vbtn-radio3&quot; autocomplete=&quot;off&quot;&gt;
    &lt;label class=&quot;btn btn-outline-danger&quot; for=&quot;vbtn-radio3&quot;&gt;Radio 3&lt;/label&gt;
&lt;/div&gt;</code>
</pre>
                                    </div>

                                </div>
                                <div class="col-lg-3 col-sm-12 d-none d-sm-block">
                                    <div class="sticky-lg-top">
                                        <strong class="d-block h6 my-2 pb-2 border-bottom">On this page</strong>
                                        <nav>
                                            <ul>
                                                <li><a href="#basic-example">Basic example</a></li>
                                                <li><a href="#mixed-styles">Mixed styles</a></li>
                                                <li><a href="#outlined-styles">Outlined styles</a></li>
                                                <li><a href="#checkbox-and-radio-button-groups">Checkbox and radio button groups</a></li>
                                                <li><a href="#button-toolbar">Button toolbar</a></li>
                                                <li><a href="#sizing">Sizing</a></li>
                                                <li><a href="#nesting">Nesting</a></li>
                                                <li><a href="#vertical-variation">Vertical variation</a></li>
                                            </ul>
                                        </nav>
                                    </div>
                                </div> <!-- Row end  -->
                            </div> <!-- Row end  -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer">
            <div class="container">
                <div class="col-12">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row justify-content-between align-items-center">
                                <div class="col">
                                    <p class="mb-0">© AL-UI. <span class="d-none d-sm-inline-block">2021 ThemeMakker.</span></p>
                                </div>
                                <div class="col-auto">
                                    <div class="d-flex justify-content-end">
                                        <!-- List Dot -->
                                        <ul class="list-inline mb-0">
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/about/">About</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/hire-us/">Hire us</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/all-templates/">Template</a>
                                            </li>
                                
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://themeforest.net/licenses/standard" target="_blank">License</a>
                                            </li>
                                        </ul>
                                        <!-- End List Dot -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title">Ready to build Layouts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-c/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-e/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-k/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-k.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Body Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-p/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-p.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Background BG</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-m/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-m.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Header <span class="small">(Fixed)</span></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">AL-UI Setting</h5>
                    </div>
                    <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan" class="active"><div class="cyan"></div></li>
                            <li data-theme="green"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-1">
                                <input class="form-check-input" type="checkbox" id="CheckGradient">
                                <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                            </div>
                    </div>
                    <!-- Settings: bg image -->
                    <div class="setting-img mb-3">
                        <div class="form-check form-switch imagebg-switch mb-1">
                            <input class="form-check-input" type="checkbox" id="CheckImage">
                            <label class="form-check-label" for="CheckImage">Set Background Image (Sidebar)</label>
                        </div>
                        <div class="bg-images">
                            <ul class="list-unstyled d-flex justify-content-between">
                                <li class="sidebar-img-1 sidebar-img-active"><a class="rounded sidebar-img" id="img-1" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-1.jpg" alt="" /></a></li>
                                <li class="sidebar-img-2"><a class="rounded sidebar-img" id="img-2" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-2.jpg" alt="" /></a></li>
                                <li class="sidebar-img-3"><a class="rounded sidebar-img" id="img-3" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-3.jpg" alt="" /></a></li>
                                <li class="sidebar-img-4"><a class="rounded sidebar-img" id="img-4" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-4.jpg" alt="" /></a></li>
                                <li class="sidebar-img-5"><a class="rounded sidebar-img" id="img-5" href="#"><img src="../../../assets/images/sidebar-bg/sidebar-5.jpg" alt="" /></a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>

<!-- Prism js file please do not add in your project -->
<script src="../../../assets/plugin/prismjs/prism.js"></script>

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
</body>
</html>