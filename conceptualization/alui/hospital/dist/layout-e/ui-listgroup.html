<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Hospital Management</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">
    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.e.min.css">
    <!-- Prism css file please do not add in your project -->
    <link rel="stylesheet" href="../../../assets/plugin/prismjs/prism.css">
</head>

<body>

<div id="layout-e" class="theme-green">

    <!-- Navigation -->
    <div class="header fixed-top shadow">

        <nav class="navbar navbar-light bg-dark py-2 px-2">
            <div class="container-fluid">

                <!-- Brand -->
                <a href="index.html" class="me-3 me-lg-4 brand-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                        <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
                    </svg>
                </a>

                <!-- Search -->
                <div class="h-left d-none d-sm-block">
                    <div class="input-group border rounded">
                        <button class="btn btn-outline-secondary dropdown-toggle border-0 d-none d-sm-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">Fillter</button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Action</a></li>
                            <li><a class="dropdown-item" href="#">Another action</a></li>
                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Separated link</a></li>
                        </ul>
                        <input type="text" class="form-control form-control-sm bg-transparent border-0" placeholder="Search here...">
                    </div>
                </div>

                <!-- header rightbar icon -->
                <div class="h-right flex-grow-1 justify-content-end d-flex align-items-center">
                    <div class="d-flex">
                        <a class="nav-link text-primary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                        <a class="nav-link text-primary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                            <i class="fa fa-sliders"></i>
                        </a>
                    </div>
                    <div class="dropdown notifications">
                        <a class="nav-link dropdown-toggle pulse" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-bell"></i>
                            <span class="pulse-ring"></span>
                        </a>
                        <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w380">
                                <div class="card-header border-top p-3">
                                    <h5 class="mb-0 fw-light d-flex justify-content-between">
                                        <span>Notifications Center</span>
                                        <span class="badge text-muted">14</span>
                                    </h5>
                                    <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tab-content card-body custom_scroll">
                                    <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Chris Morise</span> <small>2MIN</small></p>
                                                        <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Kelly</span> <small>1DAY</small></p>
                                                        <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Robin Shams</span> <small>13MIN</small></p>
                                                        <span class="text-muted">making it over 2000 years old</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span class="fw-bold">savera</span> <small>1DAY</small></p>
                                                        <span class="text-muted">The generated Lorem Ipsum</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                        <small class="text-muted">10:00 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                        <small class="text-muted">04:00 PM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                        <h4>No Logs right now!</h4>
                                    </div>
                                </div>
                                <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown user-profile ms-2 ms-sm-3">
                        <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                            <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                        </a>
                        <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w240">
                                <div class="card-body border-bottom">
                                    <div class="d-flex py-1">
                                        <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted"><span class="fw-bold">Chris Morise</span></p>
                                            <small class="text-muted"><EMAIL></small>
                                            <div>
                                                <a href="#" class="card-link">Sign out</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group m-2">
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile & account</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex ms-2 ms-sm-3">
                        <a class="nav-link d-block d-xl-none menu-toggle text-primary" href="#"><i class="fa fa-bars"></i></a>
                    </div>
                </div>

            </div>
        </nav>

    </div>

    <!-- sidebar -->
    <div class="sidebar px-3 py-2 py-md-3">
        <div class="d-flex flex-column h-100">
            <h4 class="sidebar-title mb-4 mt-2">AL<span>-UI Admin</span></h4>
            <form class="mb-2 mt-1">
                <div class="input-group">
                    <input type="text" class="form-control border-0" placeholder="Search...">
                </div>
            </form>

            <!-- Menu: main ul -->
            <ul class="menu-list flex-grow-1">
                <li><a class="m-link" href="index.html"><i class="fa fa-dashboard"></i> <span>Dashboard</span></a></li>
                <li><a class="m-link" href="calendar.html"><i class="fa fa-calendar"></i> <span>Calendar</span></a></li>
                <li><a class="m-link" href="chat.html"><i class="fa fa-comments"></i> <span>Chat app</span></a></li>
                <li class="collapsed">
                    <a class="m-link" data-bs-toggle="collapse" data-bs-target="#menu-Pages" href="#"><i class="fa fa-file"></i> <span>Pages</span> <span class="arrow fa fa-dot-circle-o ms-auto text-end"></span></a>

                    <!-- Menu: Sub menu ul -->
                    <ul class="sub-menu collapse" id="menu-Pages">
                        <li><a class="ms-link" href="profile.html">Profile</a></li>
                        <li><a class="ms-link" href="timeline.html">Timeline</a></li>
                        <li><a class="ms-link" href="imagegallery.html">Image Gallery</a></li>
                        <li><a class="ms-link" href="invoices.html">Invoices</a></li>
                        <li><a class="ms-link" href="pricing.html">Pricing</a></li>
                        <li><a class="ms-link" href="teamsboard.html">Teams Board</a></li>
                        <li><a class="ms-link" href="faqs.html">FAQs</a></li>
                    </ul>
                </li>
                <li class="collapsed">
                    <a class="m-link"  data-bs-toggle="collapse" data-bs-target="#menu-Authentication" href="#"><i class="fa fa-lock"></i> <span>Authentication</span> <span class="arrow fa fa-dot-circle-o ms-auto text-end"></span></a>

                    <!-- Menu: Sub menu ul -->
                    <ul class="sub-menu collapse" id="menu-Authentication">
                        <li><a class="ms-link" href="auth-signin.html">Sign in</a></li>
                        <li><a class="ms-link" href="auth-signup.html">Sign up</a></li>
                        <li><a class="ms-link" href="auth-password-reset.html">Password reset</a></li>
                        <li><a class="ms-link" href="auth-two-step.html">2-Step Authentication</a></li>
                        <li><a class="ms-link" href="auth-404.html">404</a></li>
                    </ul>
                </li>
                <li><a class="m-link" href="widget.html"><i class="fa fa-puzzle-piece"></i> <span>Widget's</span></a></li>

                <li class="divider mt-4 py-2 border-top"><small>DOCUMENTATION</small></li>
                <li><a class="m-link" href="../../documentation/stater-page.html"><i class="fa fa-user"></i> <span>Stater page</span></a></li>
                <li><a class="m-link" href="charts.html"><i class="fa fa-pie-chart"></i> <span>Chart's</span></a></li>
                <li><a class="m-link" href="table.html"><i class="fa fa-table"></i> <span>Table Example</span></a></li>
                <li class="collapsed">
                    <a class="m-link active" data-bs-toggle="collapse" data-bs-target="#menu-Components" href="#"><i class="fa fa-code"></i> <span>UI Components</span> <span class="arrow fa fa-dot-circle-o ms-auto text-end"></span></a>

                    <!-- Menu: Sub menu ul -->
                    <ul class="sub-menu collapse show" id="menu-Components">
                        <li><a class="ms-link" href="ui-alerts.html">Alerts</a></li>
                        <li><a class="ms-link" href="ui-badge.html">Badge</a></li>
                        <li><a class="ms-link" href="ui-breadcrumb.html">Breadcrumb</a></li>
                        <li><a class="ms-link" href="ui-buttons.html">Buttons</a></li>
                        <li><a class="ms-link" href="ui-card.html">Card</a></li>
                        <li><a class="ms-link" href="ui-carousel.html">Carousel</a></li>
                        <li><a class="ms-link" href="ui-collapse.html">Collapse</a></li>
                        <li><a class="ms-link" href="ui-dropdowns.html">Dropdowns</a></li>
                        <li><a class="ms-link active" href="ui-listgroup.html">List group</a></li>
                        <li><a class="ms-link" href="ui-modal.html">Modal</a></li>
                        <li><a class="ms-link" href="ui-navs.html">Navs</a></li>
                        <li><a class="ms-link" href="ui-navbar.html">Navbar</a></li>
                        <li><a class="ms-link" href="ui-pagination.html">Pagination</a></li>
                        <li><a class="ms-link" href="ui-popovers.html">Popovers</a></li>
                        <li><a class="ms-link" href="ui-progress.html">Progress</a></li>
                        <li><a class="ms-link" href="ui-scrollspy.html">Scrollspy</a></li>
                        <li><a class="ms-link" href="ui-spinners.html">Spinners</a></li>
                        <li><a class="ms-link" href="ui-toasts.html">Toasts</a></li>
                        <li><a class="ms-link" href="ui-tooltips.html">Tooltips</a></li>
                    </ul>
                </li>
                <li><a class="m-link" href="../../../documentation/index.html"><i class="fa fa-file-text"></i> <span>Documentation</span></a></li>
                <li><a class="m-link" href="../changelog.html"><i class="fa fa-pencil"></i> <span>Changelog</span> <span id="ALUIversion"></span></a></li>
            </ul>

            <!-- Menu: menu collepce btn -->
            <button type="button" class="btn btn-link sidebar-mini-btn text-light">
                <span><i class="fa fa-arrow-left"></i></span>
            </button>
        </div>
    </div>

    <!-- main body area -->
    <div class="main px-xl-5 px-lg-4 px-md-3">

        <!-- Body: Header -->
        <div class="body-header border-bottom d-flex py-3">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col">
                        <ol class="breadcrumb d-inline-flex bg-transparent p-0 m-0">
                            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                            <li class="breadcrumb-item">Components</li>
                            <li class="breadcrumb-item active">List group</li>
                        </ol>
                        <h1 class="h4 mt-1">List group</h1>
                    </div>
                    <div class="col-auto">
                        <a href="https://v5.getbootstrap.com/" class="btn btn-dark lift">GetBootstrap</a>
                    </div>
                </div>  <!-- Row end  -->
            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex py-lg-4 py-3">
            <div class="container-fluid">
                <div class="col-12">
                    <div class="bd-content">

                        <h1>List group</h1>
                        <p>List groups are a flexible and powerful component for displaying a series of content. Modify and extend them to support just about any content within.</p>
                        <div class="alert alert-danger" role="alert">
                            <strong>List group</strong> for more bootstrao components <a href="https://v5.getbootstrap.com/docs/5.0/components/list-group/" target="_blank">Bootstrap List group documentation <i class="fa fa-external-link"></i></a>
                        </div>

                        <div class="border-top mt-5 pt-3">
                            <h4 id="basic-example">Basic example</h4>
                            <p>The most basic list group is an unordered list with list items and the proper classes. Build upon it with the options that follow, or with your own CSS as needed.</p>
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview1" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML1" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3 bg-transparent">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview1" role="tabpanel">
                                            <div class="row">
                                                <div class="col-lg-3 col-md-6">
                                                    <!-- List Group: normal  -->
                                                    <ul class="list-group list-group-custom">
                                                        <li class="list-group-item">Cras justo odio</li>
                                                        <li class="list-group-item">Dapibus ac facilisis in</li>
                                                        <li class="list-group-item">Morbi leo risus</li>
                                                        <li class="list-group-item">Porta ac consectetur ac</li>
                                                        <li class="list-group-item">Vestibulum at eros</li>
                                                    </ul>
                                                </div>
                                                <div class="col-lg-3 col-md-6">
                                                    <!-- List Group: with badge left side -->
                                                    <ul class="list-group list-group-custom">
                                                        <li class="list-group-item">
                                                            <span class="badge bg-primary me-2">14</span>
                                                            Cras justo odio
                                                        </li>
                                                        <li class="list-group-item">
                                                            <span class="badge bg-danger me-2">2</span>
                                                            Dapibus ac facilisis in
                                                        </li>
                                                        <li class="list-group-item">
                                                            <span class="badge bg-info me-2">1</span>
                                                            Morbi leo risus
                                                        </li>
                                                        <li class="list-group-item">
                                                            <span class="badge bg-warning me-2">2</span>
                                                            Dapibus ac facilisis in
                                                        </li>
                                                        <li class="list-group-item">
                                                            <span class="badge bg-secondary me-2">1</span>
                                                            Morbi leo risus
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-lg-3 col-md-6">
                                                    <!-- List Group: with badge pill right side -->
                                                    <ul class="list-group list-group-custom">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            Cras justo odio
                                                            <span class="badge bg-primary rounded-pill">14</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            Dapibus ac facilisis in
                                                            <span class="badge bg-danger rounded-pill">2</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            Morbi leo risus
                                                            <span class="badge bg-info rounded-pill">1</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            Dapibus ac facilisis in
                                                            <span class="badge bg-warning rounded-pill">2</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            Morbi leo risus
                                                            <span class="badge bg-secondary rounded-pill">1</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-lg-3 col-md-6">
                                                    <!-- List Group: with checkbox -->
                                                    <ul class="list-group">
                                                        <li class="list-group-item">
                                                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                                            Cras justo odio
                                                        </li>
                                                        <li class="list-group-item">
                                                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                                            Dapibus ac facilisis in
                                                        </li>
                                                        <li class="list-group-item">
                                                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                                            Morbi leo risus
                                                        </li>
                                                        <li class="list-group-item">
                                                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                                            Porta ac consectetur ac
                                                        </li>
                                                        <li class="list-group-item">
                                                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                                            Vestibulum at eros
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML1" role="tabpanel">
<pre class="language-html" data-lang="html">
<code>&lt;!-- List Group: normal  --&gt;
&lt;ul class=&quot;list-group list-group-custom&quot;&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Cras justo odio&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Dapibus ac facilisis in&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Morbi leo risus&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Porta ac consectetur ac&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Vestibulum at eros&lt;/li&gt;
&lt;/ul&gt;

&lt;!-- List Group: with badge left side --&gt;
&lt;ul class=&quot;list-group list-group-custom&quot;&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;span class=&quot;badge bg-primary me-2&quot;&gt;14&lt;/span&gt;
        Cras justo odio
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;span class=&quot;badge bg-danger me-2&quot;&gt;2&lt;/span&gt;
        Dapibus ac facilisis in
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;span class=&quot;badge bg-info me-2&quot;&gt;1&lt;/span&gt;
        Morbi leo risus
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;span class=&quot;badge bg-warning me-2&quot;&gt;2&lt;/span&gt;
        Dapibus ac facilisis in
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;span class=&quot;badge bg-secondary me-2&quot;&gt;1&lt;/span&gt;
        Morbi leo risus
    &lt;/li&gt;
&lt;/ul&gt;

&lt;!-- List Group: with badge pill right side --&gt;
&lt;ul class=&quot;list-group list-group-custom&quot;&gt;
    &lt;li class=&quot;list-group-item d-flex justify-content-between align-items-center&quot;&gt;
        Cras justo odio
        &lt;span class=&quot;badge bg-primary rounded-pill&quot;&gt;14&lt;/span&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex justify-content-between align-items-center&quot;&gt;
        Dapibus ac facilisis in
        &lt;span class=&quot;badge bg-danger rounded-pill&quot;&gt;2&lt;/span&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex justify-content-between align-items-center&quot;&gt;
        Morbi leo risus
        &lt;span class=&quot;badge bg-info rounded-pill&quot;&gt;1&lt;/span&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex justify-content-between align-items-center&quot;&gt;
        Dapibus ac facilisis in
        &lt;span class=&quot;badge bg-warning rounded-pill&quot;&gt;2&lt;/span&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex justify-content-between align-items-center&quot;&gt;
        Morbi leo risus
        &lt;span class=&quot;badge bg-secondary rounded-pill&quot;&gt;1&lt;/span&gt;
    &lt;/li&gt;
&lt;/ul&gt;

&lt;!-- List Group: with checkbox --&gt;
&lt;ul class=&quot;list-group&quot;&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;input class=&quot;form-check-input me-1&quot; type=&quot;checkbox&quot; value=&quot;&quot; aria-label=&quot;...&quot;&gt;
        Cras justo odio
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;input class=&quot;form-check-input me-1&quot; type=&quot;checkbox&quot; value=&quot;&quot; aria-label=&quot;...&quot;&gt;
        Dapibus ac facilisis in
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;input class=&quot;form-check-input me-1&quot; type=&quot;checkbox&quot; value=&quot;&quot; aria-label=&quot;...&quot;&gt;
        Morbi leo risus
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;input class=&quot;form-check-input me-1&quot; type=&quot;checkbox&quot; value=&quot;&quot; aria-label=&quot;...&quot;&gt;
        Porta ac consectetur ac
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;
        &lt;input class=&quot;form-check-input me-1&quot; type=&quot;checkbox&quot; value=&quot;&quot; aria-label=&quot;...&quot;&gt;
        Vestibulum at eros
    &lt;/li&gt;
&lt;/ul&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview1b" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML1b" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3 bg-transparent">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview1b" role="tabpanel">
                                            <div class="row">
                                                <div class="col-lg-4 col-md-12">
                                                    <!-- List Group: User list  -->
                                                    <ul class="list-unstyled list-group list-group-custom list-group-flush mb-0">
                                                        <li class="list-group-item px-md-4 py-3">
                                                            <a href="javascript:void(0);" class="d-flex">
                                                                <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <h6 class="d-flex justify-content-between mb-0"><span>Chris Fox</span></h6>
                                                                    <span class="text-muted"><EMAIL></span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li class="list-group-item px-md-4 py-3">
                                                            <a href="javascript:void(0);" class="d-flex">
                                                                <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <h6 class="d-flex justify-content-between mb-0"><span>Dr. Hossein Shams</span></h6>
                                                                    <span class="text-muted"><EMAIL></span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li class="list-group-item px-md-4 py-3">
                                                            <a href="javascript:void(0);" class="d-flex">
                                                                <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <h6 class="d-flex justify-content-between mb-0"><span>Orlando Lentz</span></h6>
                                                                    <span class="text-muted"><EMAIL></span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li class="list-group-item px-md-4 py-3">
                                                            <a href="javascript:void(0);" class="d-flex">
                                                                <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <h6 class="d-flex justify-content-between mb-0"><span>Barbara Kelly</span></h6>
                                                                    <span class="text-muted"><EMAIL></span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li class="list-group-item px-md-4 py-3">
                                                            <a href="javascript:void(0);" class="d-flex">
                                                                <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <h6 class="d-flex justify-content-between mb-0"><span>Dr. Hossein Shams</span></h6>
                                                                    <span class="text-muted"><EMAIL></span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-lg-4 col-md-12">
                                                    <!-- List Group: Notification -->
                                                    <ul class="list-unstyled list-group list-group-custom list-group-flush mb-0">
                                                        <li class="list-group-item d-flex py-3">
                                                            <div class="avatar"><i class="fa fa-thumbs-o-up fa-lg"></i></div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-0">7 New Feedback <small class="float-right text-muted">Today</small></h6>
                                                                <small>It will give a smart finishing to your site</small>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex py-3">
                                                            <div class="avatar"><i class="fa fa-user fa-lg"></i></div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-0">New User <small class="float-right text-muted">10:45</small></h6>
                                                                <small>I feel great! Thanks team</small>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex py-3">
                                                            <div class="avatar"><i class="fa fa-question-circle fa-lg"></i></div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-0 text-warning">Server Warning <small class="float-right text-muted">10:50</small></h6>
                                                                <small>Your connection is not private</small>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex py-3">
                                                            <div class="avatar"><i class="fa fa-check fa-lg"></i></div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-0 text-danger">Issue Fixed <small class="float-right text-muted">11:05</small></h6>
                                                                <small>WE have fix all Design bug with Responsive</small>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex py-3">
                                                            <div class="avatar"><i class="fa fa-shopping-basket fa-lg"></i></div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-0">7 New Orders <small class="float-right text-muted">11:35</small></h6>
                                                                <small>You received a new oder from Tina.</small>
                                                            </div>
                                                        </li>                                   
                                                    </ul>
                                                </div>
                                                <div class="col-lg-4 col-md-12">
                                                    <!-- List Group: iOT list with switch -->
                                                    <ul class="list-unstyled list-group list-group-custom list-group-flush mb-0">
                                                        <li class="list-group-item d-flex align-items-center py-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="list-group1">
                                                                <label class="form-check-label" for="list-group1">Front Door</label>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center py-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="list-group2" checked="">
                                                                <label class="form-check-label" for="list-group2">Air Conditioner</label>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center py-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="list-group3">
                                                                <label class="form-check-label" for="list-group3">Enable RTL Mode!</label>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center py-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="list-group4">
                                                                <label class="form-check-label" for="list-group4">Front Door</label>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center py-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="list-group5">
                                                                <label class="form-check-label" for="list-group5">Air Conditioner</label>
                                                            </div>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center py-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="list-group6" checked="">
                                                                <label class="form-check-label" for="list-group6">Washing Machine</label>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML1b" role="tabpanel">
<pre class="language-html" data-lang="html">
<code>&lt;!-- List Group: User list  --&gt;
&lt;ul class=&quot;list-unstyled list-group list-group-custom list-group-flush mb-0&quot;&gt;
    &lt;li class=&quot;list-group-item px-md-4 py-3&quot;&gt;
        &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
            &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar1.jpg&quot; alt=&quot;&quot;&gt;
            &lt;div class=&quot;flex-fill ms-3 text-truncate&quot;&gt;
                &lt;h6 class=&quot;d-flex justify-content-between mb-0&quot;&gt;&lt;span&gt;Chris Fox&lt;/span&gt;&lt;/h6&gt;
                &lt;span class=&quot;text-muted&quot;&gt;<EMAIL>&lt;/span&gt;
            &lt;/div&gt;
        &lt;/a&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item px-md-4 py-3&quot;&gt;
        &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
            &lt;div class=&quot;avatar rounded-circle no-thumbnail&quot;&gt;RH&lt;/div&gt;
            &lt;div class=&quot;flex-fill ms-3 text-truncate&quot;&gt;
                &lt;h6 class=&quot;d-flex justify-content-between mb-0&quot;&gt;&lt;span&gt;Dr. Hossein Shams&lt;/span&gt;&lt;/h6&gt;
                &lt;span class=&quot;text-muted&quot;&gt;<EMAIL>&lt;/span&gt;
            &lt;/div&gt;
        &lt;/a&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item px-md-4 py-3&quot;&gt;
        &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
            &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar3.jpg&quot; alt=&quot;&quot;&gt;
            &lt;div class=&quot;flex-fill ms-3 text-truncate&quot;&gt;
                &lt;h6 class=&quot;d-flex justify-content-between mb-0&quot;&gt;&lt;span&gt;Orlando Lentz&lt;/span&gt;&lt;/h6&gt;
                &lt;span class=&quot;text-muted&quot;&gt;<EMAIL>&lt;/span&gt;
            &lt;/div&gt;
        &lt;/a&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item px-md-4 py-3&quot;&gt;
        &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
            &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar4.jpg&quot; alt=&quot;&quot;&gt;
            &lt;div class=&quot;flex-fill ms-3 text-truncate&quot;&gt;
                &lt;h6 class=&quot;d-flex justify-content-between mb-0&quot;&gt;&lt;span&gt;Barbara Kelly&lt;/span&gt;&lt;/h6&gt;
                &lt;span class=&quot;text-muted&quot;&gt;<EMAIL>&lt;/span&gt;
            &lt;/div&gt;
        &lt;/a&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item px-md-4 py-3&quot;&gt;
        &lt;a href=&quot;javascript:void(0);&quot; class=&quot;d-flex&quot;&gt;
            &lt;img class=&quot;avatar rounded-circle&quot; src=&quot;assets/images/xs/avatar5.jpg&quot; alt=&quot;&quot;&gt;
            &lt;div class=&quot;flex-fill ms-3 text-truncate&quot;&gt;
                &lt;h6 class=&quot;d-flex justify-content-between mb-0&quot;&gt;&lt;span&gt;Dr. Hossein Shams&lt;/span&gt;&lt;/h6&gt;
                &lt;span class=&quot;text-muted&quot;&gt;<EMAIL>&lt;/span&gt;
            &lt;/div&gt;
        &lt;/a&gt;
    &lt;/li&gt;
&lt;/ul&gt;

&lt;!-- List Group: Notification --&gt;
&lt;ul class=&quot;list-unstyled list-group list-group-custom list-group-flush mb-0&quot;&gt;
    &lt;li class=&quot;list-group-item d-flex py-3&quot;&gt;
        &lt;div class=&quot;avatar&quot;&gt;&lt;i class=&quot;fa fa-thumbs-o-up fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
        &lt;div class=&quot;flex-grow-1&quot;&gt;
            &lt;h6 class=&quot;mb-0&quot;&gt;7 New Feedback &lt;small class=&quot;float-right text-muted&quot;&gt;Today&lt;/small&gt;&lt;/h6&gt;
            &lt;small&gt;It will give a smart finishing to your site&lt;/small&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex py-3&quot;&gt;
        &lt;div class=&quot;avatar&quot;&gt;&lt;i class=&quot;fa fa-user fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
        &lt;div class=&quot;flex-grow-1&quot;&gt;
            &lt;h6 class=&quot;mb-0&quot;&gt;New User &lt;small class=&quot;float-right text-muted&quot;&gt;10:45&lt;/small&gt;&lt;/h6&gt;
            &lt;small&gt;I feel great! Thanks team&lt;/small&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex py-3&quot;&gt;
        &lt;div class=&quot;avatar&quot;&gt;&lt;i class=&quot;fa fa-question-circle fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
        &lt;div class=&quot;flex-grow-1&quot;&gt;
            &lt;h6 class=&quot;mb-0 text-warning&quot;&gt;Server Warning &lt;small class=&quot;float-right text-muted&quot;&gt;10:50&lt;/small&gt;&lt;/h6&gt;
            &lt;small&gt;Your connection is not private&lt;/small&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex py-3&quot;&gt;
        &lt;div class=&quot;avatar&quot;&gt;&lt;i class=&quot;fa fa-check fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
        &lt;div class=&quot;flex-grow-1&quot;&gt;
            &lt;h6 class=&quot;mb-0 text-danger&quot;&gt;Issue Fixed &lt;small class=&quot;float-right text-muted&quot;&gt;11:05&lt;/small&gt;&lt;/h6&gt;
            &lt;small&gt;WE have fix all Design bug with Responsive&lt;/small&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex py-3&quot;&gt;
        &lt;div class=&quot;avatar&quot;&gt;&lt;i class=&quot;fa fa-shopping-basket fa-lg&quot;&gt;&lt;/i&gt;&lt;/div&gt;
        &lt;div class=&quot;flex-grow-1&quot;&gt;
            &lt;h6 class=&quot;mb-0&quot;&gt;7 New Orders &lt;small class=&quot;float-right text-muted&quot;&gt;11:35&lt;/small&gt;&lt;/h6&gt;
            &lt;small&gt;You received a new oder from Tina.&lt;/small&gt;
        &lt;/div&gt;
    &lt;/li&gt;                                   
&lt;/ul&gt;

&lt;!-- List Group: iOT list with switch --&gt;
&lt;ul class=&quot;list-unstyled list-group list-group-custom list-group-flush mb-0&quot;&gt;
    &lt;li class=&quot;list-group-item d-flex align-items-center py-3&quot;&gt;
        &lt;div class=&quot;form-check form-switch&quot;&gt;
            &lt;input class=&quot;form-check-input&quot; type=&quot;checkbox&quot; id=&quot;list-group1&quot;&gt;
            &lt;label class=&quot;form-check-label&quot; for=&quot;list-group1&quot;&gt;Front Door&lt;/label&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex align-items-center py-3&quot;&gt;
        &lt;div class=&quot;form-check form-switch&quot;&gt;
            &lt;input class=&quot;form-check-input&quot; type=&quot;checkbox&quot; id=&quot;list-group2&quot; checked=&quot;&quot;&gt;
            &lt;label class=&quot;form-check-label&quot; for=&quot;list-group2&quot;&gt;Air Conditioner&lt;/label&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex align-items-center py-3&quot;&gt;
        &lt;div class=&quot;form-check form-switch&quot;&gt;
            &lt;input class=&quot;form-check-input&quot; type=&quot;checkbox&quot; id=&quot;list-group3&quot;&gt;
            &lt;label class=&quot;form-check-label&quot; for=&quot;list-group3&quot;&gt;Enable RTL Mode!&lt;/label&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex align-items-center py-3&quot;&gt;
        &lt;div class=&quot;form-check form-switch&quot;&gt;
            &lt;input class=&quot;form-check-input&quot; type=&quot;checkbox&quot; id=&quot;list-group4&quot;&gt;
            &lt;label class=&quot;form-check-label&quot; for=&quot;list-group4&quot;&gt;Front Door&lt;/label&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex align-items-center py-3&quot;&gt;
        &lt;div class=&quot;form-check form-switch&quot;&gt;
            &lt;input class=&quot;form-check-input&quot; type=&quot;checkbox&quot; id=&quot;list-group5&quot;&gt;
            &lt;label class=&quot;form-check-label&quot; for=&quot;list-group5&quot;&gt;Air Conditioner&lt;/label&gt;
        &lt;/div&gt;
    &lt;/li&gt;
    &lt;li class=&quot;list-group-item d-flex align-items-center py-3&quot;&gt;
        &lt;div class=&quot;form-check form-switch&quot;&gt;
            &lt;input class=&quot;form-check-input&quot; type=&quot;checkbox&quot; id=&quot;list-group6&quot; checked=&quot;&quot;&gt;
            &lt;label class=&quot;form-check-label&quot; for=&quot;list-group6&quot;&gt;Washing Machine&lt;/label&gt;
        &lt;/div&gt;
    &lt;/li&gt;
&lt;/ul&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h5 id="active-items">Active & Disabled items</h5>
                            <p class="mb-0">Add <code>.active</code> to a <code>.list-group-item</code> to indicate the current active selection.</p>
                            <p>Add <code>.disabled</code> to a <code>.list-group-item</code> to make it <em>appear</em> disabled. Note that some elements with <code>.disabled</code> will also require custom JavaScript to fully disable their click events (e.g., links).</p>
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview2" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML2" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3 bg-transparent">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview2" role="tabpanel">
                                            <ul class="list-group list-group-custom" style="max-width: 400px;">
                                                <li class="list-group-item active" aria-current="true">Cras justo odio</li>
                                                <li class="list-group-item">Dapibus ac facilisis in</li>
                                                <li class="list-group-item">Morbi leo risus</li>
                                                <li class="list-group-item disabled">Porta ac consectetur ac</li>
                                                <li class="list-group-item">Vestibulum at eros</li>
                                            </ul>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML2" role="tabpanel">
<pre class="language-html" data-lang="html">
<code>&lt;ul class=&quot;list-group list-group-custom&quot;&gt;
    &lt;li class=&quot;list-group-item active&quot; aria-current=&quot;true&quot;&gt;Cras justo odio&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Dapibus ac facilisis in&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Morbi leo risus&lt;/li&gt;
    &lt;li class=&quot;list-group-item disabled&quot;&gt;Porta ac consectetur ac&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Vestibulum at eros&lt;/li&gt;
&lt;/ul&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border-top mt-5 pt-3">
                            <h2 id="flush">Flush</h2>
                            <p>Add <code>.list-group-flush</code> to remove some borders and rounded corners to render list group items edge-to-edge in a parent container (e.g., cards).</p>
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview3" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML3" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3 bg-transparent">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview3" role="tabpanel">
                                            <ul class="list-group list-group-flush list-group-custom" style="max-width: 400px;">
                                                <li class="list-group-item">Cras justo odio</li>
                                                <li class="list-group-item">Dapibus ac facilisis in</li>
                                                <li class="list-group-item">Morbi leo risus</li>
                                                <li class="list-group-item">Porta ac consectetur ac</li>
                                                <li class="list-group-item">Vestibulum at eros</li>
                                            </ul>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML3" role="tabpanel">
<pre class="language-html" data-lang="html">
<code>&lt;ul class=&quot;list-group list-group-flush&quot;&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Cras justo odio&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Dapibus ac facilisis in&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Morbi leo risus&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Porta ac consectetur ac&lt;/li&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Vestibulum at eros&lt;/li&gt;
&lt;/ul&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border-top mt-5 pt-3">
                            <h2 id="contextual-classes">Contextual classes</h2>
                            <p>Use contextual classes to style list items with a stateful background and color.</p>
                            <p>Contextual classes also work with <code>.list-group-item-action</code>. Note the addition of the hover styles here not present in the previous example. Also supported is the <code>.active</code> state; apply it to indicate an active selection on a contextual list group item.</p>
                            <ul class="nav nav-tabs tab-card px-3 border-bottom-0" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Preview4" role="tab">Preview</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#nav-HTML4" role="tab">HTML</a></li>
                            </ul>
                            <div class="card mb-3 bg-transparent">
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="nav-Preview4" role="tabpanel">
                                            <div class="row">
                                                <div class="col-lg-6 col-md-12">
                                                    <p>Simple list group</p>
                                                    <ul class="list-group">
                                                        <li class="list-group-item">Dapibus ac facilisis in</li>
                                                    
                                                        <li class="list-group-item list-group-item-primary">A simple primary list group item</li>
                                                        <li class="list-group-item list-group-item-secondary">A simple secondary list group item</li>
                                                        <li class="list-group-item list-group-item-success">A simple success list group item</li>
                                                        <li class="list-group-item list-group-item-danger">A simple danger list group item</li>
                                                        <li class="list-group-item list-group-item-warning">A simple warning list group item</li>
                                                        <li class="list-group-item list-group-item-info">A simple info list group item</li>
                                                        <li class="list-group-item list-group-item-light">A simple light list group item</li>
                                                        <li class="list-group-item list-group-item-dark">A simple dark list group item</li>
                                                    </ul>
                                                </div>
                                                <div class="col-lg-6 col-md-12">
                                                    <p>list gorup with Anchor Link tag</p>
                                                    <div class="list-group">
                                                        <a href="#" class="list-group-item list-group-item-action">Dapibus ac facilisis in</a>
                                                    
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-primary">A simple primary list group item</a>
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-secondary">A simple secondary list group item</a>
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-success">A simple success list group item</a>
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-danger">A simple danger list group item</a>
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-warning">A simple warning list group item</a>
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-info">A simple info list group item</a>
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-light">A simple light list group item</a>
                                                        <a href="#" class="list-group-item list-group-item-action list-group-item-dark">A simple dark list group item</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-HTML4" role="tabpanel">
<pre class="language-html" data-lang="html">
<code>&lt;ul class=&quot;list-group&quot;&gt;
    &lt;li class=&quot;list-group-item&quot;&gt;Dapibus ac facilisis in&lt;/li&gt;
    
    &lt;li class=&quot;list-group-item list-group-item-primary&quot;&gt;A simple primary list group item&lt;/li&gt;
    &lt;li class=&quot;list-group-item list-group-item-secondary&quot;&gt;A simple secondary list group item&lt;/li&gt;
    &lt;li class=&quot;list-group-item list-group-item-success&quot;&gt;A simple success list group item&lt;/li&gt;
    &lt;li class=&quot;list-group-item list-group-item-danger&quot;&gt;A simple danger list group item&lt;/li&gt;
    &lt;li class=&quot;list-group-item list-group-item-warning&quot;&gt;A simple warning list group item&lt;/li&gt;
    &lt;li class=&quot;list-group-item list-group-item-info&quot;&gt;A simple info list group item&lt;/li&gt;
    &lt;li class=&quot;list-group-item list-group-item-light&quot;&gt;A simple light list group item&lt;/li&gt;
    &lt;li class=&quot;list-group-item list-group-item-dark&quot;&gt;A simple dark list group item&lt;/li&gt;
&lt;/ul&gt;

&lt;div class=&quot;list-group&quot;&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action&quot;&gt;Dapibus ac facilisis in&lt;/a&gt;
    
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-primary&quot;&gt;A simple primary list group item&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-secondary&quot;&gt;A simple secondary list group item&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-success&quot;&gt;A simple success list group item&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-danger&quot;&gt;A simple danger list group item&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-warning&quot;&gt;A simple warning list group item&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-info&quot;&gt;A simple info list group item&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-light&quot;&gt;A simple light list group item&lt;/a&gt;
    &lt;a href=&quot;#&quot; class=&quot;list-group-item list-group-item-action list-group-item-dark&quot;&gt;A simple dark list group item&lt;/a&gt;
&lt;/div&gt;</code>
</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer">
            <div class="container-fluid">
                <div class="col-12">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row justify-content-between align-items-center">
                                <div class="col">
                                    <p class="mb-0">© AL-UI. <span class="d-none d-sm-inline-block">2021 ThemeMakker.</span></p>
                                </div>
                                <div class="col-auto">
                                    <div class="d-flex justify-content-end">
                                        <!-- List Dot -->
                                        <ul class="list-inline mb-0">
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/about/">About</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/hire-us/">Hire us</a>
                                            </li>
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://www.thememakker.com/all-templates/">Template</a>
                                            </li>
                                
                                            <li class="list-inline-item">
                                                <a class="list-separator-link" href="https://themeforest.net/licenses/standard" target="_blank">License</a>
                                            </li>
                                        </ul>
                                        <!-- End List Dot -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div><!-- Modal: Search -->
    <div class="modal fade" id="SearchModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-secondary border-bottom-0 px-3 px-md-5">
                    <h5 class="modal-title" >Search</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body custom_scroll">
                    <div class="card-body-height py-4 px-2 px-md-4">
                        <form class="mb-3">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search...">
                                <button class="btn btn-outline-secondary" type="button"><span class="fa fa-search"></span> Search</button>
                            </div>
                        </form>

                        <small class="dropdown-header">Recent searches</small>              
                        <div class="dropdown-item bg-transparent text-wrap my-2">
                            <span class="h4 me-1">
                                <a class="btn btn-sm btn-dark" href="#">Github <i class="fa fa-search ms-1"></i></a>
                            </span>
                            <span class="h4">
                                <a class="btn btn-sm btn-dark" href="#">Notification panel <i class="fa fa-search ms-1"></i></a>
                            </span>
                            <span class="h4">
                                <a class="btn btn-sm btn-dark" href="#">New project <i class="fa fa-search ms-1"></i></a>
                            </span>
                        </div>
            
                        <div class="dropdown-divider my-3"></div>
                        
                        <small class="dropdown-header">Tutorials</small>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <span class="avatar sm no-thumbnail me-2"><i class="fa fa-github"></i></span>
                                <div class="text-truncate">
                                    <span>How to set up Github?</span>
                                </div>
                            </div>
                        </a>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <span class="avatar sm no-thumbnail me-2"><i class="fa fa-paint-brush"></i></span>
                                <div class="text-truncate">
                                    <span>How to change theme color?</span>
                                </div>
                            </div>
                        </a>
            
                        <div class="dropdown-divider my-3"></div>
            
                        <small class="dropdown-header">Members</small>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                <div class="text-truncate ms-2">
                                    <span>Dr. Hossein Shams <i class="fa fa-check-circle text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="" data-original-title="Top endorsed"></i></span>
                                </div>
                            </div>
                        </a>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                <div class="text-truncate ms-2">
                                    <span>Orlando Lentz</span>
                                </div>
                            </div>
                        </a>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <div class="avatar sm rounded-circle no-thumbnail">RH</div>
                                <div class="text-truncate ms-2">
                                    <span>Brian Swader</span>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title">Ready to build Layouts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-c/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-k/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-k.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Body Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-p/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-p.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Background BG</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-m/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-m.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Header <span class="small">(Fixed)</span></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">AL-UI Setting</h5>
                    </div>
                    <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan"><div class="cyan"></div></li>
                            <li data-theme="green" class="active"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="CheckGradient">
                            <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>

<!-- Prism js file please do not add in your project -->
<script src="../../../assets/plugin/prismjs/prism.js"></script>

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
</body>
</html>