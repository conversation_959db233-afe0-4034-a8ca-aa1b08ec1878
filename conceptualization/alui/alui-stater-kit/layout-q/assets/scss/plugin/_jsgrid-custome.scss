// Admin Layout (ALUI)
// @author: WrapTheme ( https://themeforest.net/user/wrraptheme )
// @design by: ThemeMakker Infotech LLP.
// @event-namespace: ALUI
// Copyright 2021 WrapTheme

.jsgrid{
    .jsgrid-grid-header{
        background: var(--border-color);
        border-color: var(--border-color);
    }
}

.jsgrid-header-row{
    >.jsgrid-header-cell{
        background: var(--border-color);
    }
}

.jsgrid-edit-row>.jsgrid-cell,
.jsgrid-filter-row>.jsgrid-cell,
.jsgrid-grid-body,
.jsgrid-grid-header,
.jsgrid-header-row>.jsgrid-header-cell,
.jsgrid-insert-row>.jsgrid-cell{
    border-color: var(--border-color);
}
.jsgrid-row{
    >.jsgrid-cell{
        background: var(--card-color);
        border-color: var(--border-color);
    }
}
.jsgrid-alt-row{
    >.jsgrid-cell{
        background: var(--color-100);
        border-color: var(--border-color);
    }
}