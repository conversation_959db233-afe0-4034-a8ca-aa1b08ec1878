from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from routes.auth import login_required
import subprocess
import os
import signal
import time
import threading
from pathlib import Path

notebooks_bp = Blueprint('notebooks', __name__, url_prefix='/notebooks')

# Store running notebook processes
notebook_processes = {}
notebook_ports = {'python': 8888, 'r': 8889}

def get_notebook_files():
    """Get list of notebook files in the notebooks directory"""
    notebooks_dir = Path('notebooks')
    notebooks_dir.mkdir(exist_ok=True)

    notebook_files = []
    for file_path in notebooks_dir.glob('*.ipynb'):
        try:
            stat = file_path.stat()
            notebook_files.append({
                'name': file_path.name,
                'path': str(file_path.relative_to(notebooks_dir)),
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'modified': stat.st_mtime,
                'modified_str': time.strftime('%Y-%m-%d %H:%M', time.localtime(stat.st_mtime))
            })
        except Exception as e:
            print(f"Error reading notebook {file_path}: {e}")

    return sorted(notebook_files, key=lambda x: x['modified'], reverse=True)

@notebooks_bp.route('/')
@login_required
def index():
    """JupyterLite Notebooks interface"""
    notebook_files = get_notebook_files()
    return render_template('notebooks/jupyterlite.html', notebook_files=notebook_files)

def is_port_in_use(port):
    """Check if a port is already in use"""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def wait_for_server_start(port, timeout=30):
    """Wait for Jupyter server to start on the specified port"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if is_port_in_use(port):
            return True
        time.sleep(1)
    return False

@notebooks_bp.route('/start/<kernel_type>')
@login_required
def start_notebook(kernel_type):
    """Start Jupyter notebook with specified kernel"""
    try:
        if kernel_type not in ['python', 'r']:
            return jsonify({
                'success': False,
                'error': 'Invalid kernel type. Must be "python" or "r"'
            })

        # Create notebooks directory if it doesn't exist
        notebooks_dir = Path('notebooks')
        notebooks_dir.mkdir(exist_ok=True)

        port = notebook_ports[kernel_type]

        # Check if notebook is already running for this kernel
        if kernel_type in notebook_processes:
            process = notebook_processes[kernel_type]
            if process.poll() is None:  # Process is still running
                return jsonify({
                    'success': True,
                    'message': f'{kernel_type.title()} notebook is already running',
                    'url': f'http://localhost:{port}'
                })

        # Check if port is already in use by another process
        if is_port_in_use(port):
            return jsonify({
                'success': False,
                'error': f'Port {port} is already in use. Please stop any existing Jupyter servers on this port.'
            })

        # Prepare Jupyter command using configuration file for iframe embedding
        config_file = Path('jupyter_config.py').absolute()
        cmd = [
            'jupyter', 'notebook',
            f'--config={config_file}',
            f'--port={port}',
            f'--notebook-dir={notebooks_dir.absolute()}'
        ]

        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=str(notebooks_dir.absolute()),
            env=os.environ.copy()
        )

        # Wait a moment for the server to start
        time.sleep(2)

        # Check if process started successfully
        if process.poll() is not None:
            # Process has already terminated
            stdout, stderr = process.communicate()
            return jsonify({
                'success': False,
                'error': f'Failed to start Jupyter server. Error: {stderr.decode()}'
            })

        notebook_processes[kernel_type] = process

        # Wait for server to be ready
        if wait_for_server_start(port, timeout=10):
            return jsonify({
                'success': True,
                'message': f'{kernel_type.title()} notebook started successfully',
                'url': f'http://localhost:{port}',
                'pid': process.pid
            })
        else:
            # Server didn't start in time, kill the process
            process.terminate()
            if kernel_type in notebook_processes:
                del notebook_processes[kernel_type]
            return jsonify({
                'success': False,
                'error': 'Jupyter server started but is not responding. Please try again.'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error starting notebook: {str(e)}'
        })

@notebooks_bp.route('/stop/<kernel_type>')
@login_required
def stop_notebook(kernel_type):
    """Stop Jupyter notebook for specified kernel"""
    try:
        if kernel_type not in notebook_processes:
            # Check if there's a process running on the port anyway
            port = notebook_ports.get(kernel_type)
            if port and is_port_in_use(port):
                return jsonify({
                    'success': False,
                    'error': f'Jupyter server is running on port {port} but not managed by Harmattan. Please stop it manually.'
                })
            return jsonify({
                'success': False,
                'error': f'No {kernel_type} notebook process found'
            })

        process = notebook_processes[kernel_type]

        # Terminate the process gracefully
        if process.poll() is None:  # Process is still running
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # Force kill if it doesn't terminate gracefully
                process.kill()
                process.wait()

        del notebook_processes[kernel_type]

        return jsonify({
            'success': True,
            'message': f'{kernel_type.title()} notebook stopped successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error stopping notebook: {str(e)}'
        })

@notebooks_bp.route('/status')
@login_required
def status():
    """Get status of running notebooks"""
    try:
        status_info = {}

        # Check all kernel types
        for kernel_type in ['python', 'r']:
            port = notebook_ports[kernel_type]

            if kernel_type in notebook_processes:
                process = notebook_processes[kernel_type]
                if process.poll() is None:  # Process is still running
                    status_info[kernel_type] = {
                        'running': True,
                        'pid': process.pid,
                        'url': f'http://localhost:{port}'
                    }
                else:
                    # Process has died, remove it from tracking
                    del notebook_processes[kernel_type]
                    status_info[kernel_type] = {
                        'running': False
                    }
            else:
                # Check if there's a server running on the port (not managed by us)
                if is_port_in_use(port):
                    status_info[kernel_type] = {
                        'running': True,
                        'pid': 'unknown',
                        'url': f'http://localhost:{port}',
                        'external': True
                    }
                else:
                    status_info[kernel_type] = {
                        'running': False
                    }

        return jsonify({
            'success': True,
            'status': status_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@notebooks_bp.route('/files')
@login_required
def get_files():
    """Get list of notebook files"""
    try:
        notebook_files = get_notebook_files()
        return jsonify({
            'success': True,
            'files': notebook_files
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@notebooks_bp.route('/open/<path:notebook_path>')
@login_required
def open_notebook(notebook_path):
    """Open a specific notebook file"""
    try:
        notebooks_dir = Path('notebooks')
        notebook_file = notebooks_dir / notebook_path

        # Security check - ensure the file is within the notebooks directory
        if not str(notebook_file.resolve()).startswith(str(notebooks_dir.resolve())):
            return jsonify({
                'success': False,
                'error': 'Invalid notebook path'
            })

        if not notebook_file.exists():
            return jsonify({
                'success': False,
                'error': 'Notebook file not found'
            })

        # Check if any Jupyter server is running
        running_servers = []
        for kernel_type in ['python', 'r']:
            port = notebook_ports[kernel_type]
            if is_port_in_use(port):
                running_servers.append({
                    'kernel': kernel_type,
                    'port': port,
                    'url': f'http://localhost:{port}/notebooks/{notebook_path}'
                })

        if running_servers:
            return jsonify({
                'success': True,
                'servers': running_servers,
                'message': 'Notebook can be opened in running Jupyter servers'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'No Jupyter servers are running. Please start a notebook server first.'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@notebooks_bp.route('/create', methods=['POST'])
@login_required
def create_notebook():
    """Create a new notebook file"""
    try:
        data = request.get_json()
        notebook_name = data.get('name', '').strip()
        kernel_type = data.get('kernel', 'python')

        if not notebook_name:
            return jsonify({
                'success': False,
                'error': 'Notebook name is required'
            })

        # Ensure .ipynb extension
        if not notebook_name.endswith('.ipynb'):
            notebook_name += '.ipynb'

        notebooks_dir = Path('notebooks')
        notebooks_dir.mkdir(exist_ok=True)
        notebook_path = notebooks_dir / notebook_name

        # Check if file already exists
        if notebook_path.exists():
            return jsonify({
                'success': False,
                'error': 'A notebook with this name already exists'
            })

        # Create basic notebook structure
        notebook_content = {
            "cells": [
                {
                    "cell_type": "markdown",
                    "metadata": {},
                    "source": [
                        f"# {notebook_name.replace('.ipynb', '')}\n",
                        "\n",
                        "This notebook was created in Harmattan Analytics Platform.\n",
                        "\n",
                        "## Getting Started\n",
                        "\n",
                        "You can access your Harmattan database using:\n",
                        "\n",
                        "```python\n",
                        "import duckdb\n",
                        "conn = duckdb.connect('../dbs/harmattan.db')\n",
                        "df = conn.execute('SELECT * FROM your_table').df()\n",
                        "```"
                    ]
                },
                {
                    "cell_type": "code",
                    "execution_count": None,
                    "metadata": {},
                    "outputs": [],
                    "source": [
                        "# Import required libraries\n",
                        "import pandas as pd\n",
                        "import numpy as np\n",
                        "import matplotlib.pyplot as plt\n",
                        "\n",
                        "print('Welcome to Harmattan Analytics!')"
                    ]
                }
            ],
            "metadata": {
                "kernelspec": {
                    "display_name": "Python 3" if kernel_type == 'python' else "R",
                    "language": kernel_type,
                    "name": "python3" if kernel_type == 'python' else "ir"
                },
                "language_info": {
                    "name": kernel_type,
                    "version": "3.8.0" if kernel_type == 'python' else "4.0.0"
                }
            },
            "nbformat": 4,
            "nbformat_minor": 4
        }

        # Write notebook file
        import json
        with open(notebook_path, 'w') as f:
            json.dump(notebook_content, f, indent=2)

        return jsonify({
            'success': True,
            'message': f'Notebook "{notebook_name}" created successfully',
            'path': notebook_name
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@notebooks_bp.route('/restart_all')
@login_required
def restart_all_notebooks():
    """Restart all notebook servers with new configuration"""
    try:
        restarted = []

        # Stop all running notebooks
        for kernel_type in list(notebook_processes.keys()):
            if kernel_type in notebook_processes:
                process = notebook_processes[kernel_type]
                if process.poll() is None:  # Process is still running
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        process.wait()
                del notebook_processes[kernel_type]
                restarted.append(kernel_type)

        return jsonify({
            'success': True,
            'message': f'Restarted {len(restarted)} notebook servers with new configuration',
            'restarted': restarted
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })
