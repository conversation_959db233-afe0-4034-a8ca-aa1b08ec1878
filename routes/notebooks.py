from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from routes.auth import login_required
import subprocess
import os
import signal
import time
import threading
from pathlib import Path

notebooks_bp = Blueprint('notebooks', __name__, url_prefix='/notebooks')

# Store running notebook processes
notebook_processes = {}
notebook_ports = {'python': 8888, 'r': 8889}

@notebooks_bp.route('/')
@login_required
def index():
    """Jupyter Notebooks interface"""
    return render_template('notebooks/index.html')

def is_port_in_use(port):
    """Check if a port is already in use"""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def wait_for_server_start(port, timeout=30):
    """Wait for Jupyter server to start on the specified port"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if is_port_in_use(port):
            return True
        time.sleep(1)
    return False

@notebooks_bp.route('/start/<kernel_type>')
@login_required
def start_notebook(kernel_type):
    """Start Jupyter notebook with specified kernel"""
    try:
        if kernel_type not in ['python', 'r']:
            return jsonify({
                'success': False,
                'error': 'Invalid kernel type. Must be "python" or "r"'
            })

        # Create notebooks directory if it doesn't exist
        notebooks_dir = Path('notebooks')
        notebooks_dir.mkdir(exist_ok=True)

        port = notebook_ports[kernel_type]

        # Check if notebook is already running for this kernel
        if kernel_type in notebook_processes:
            process = notebook_processes[kernel_type]
            if process.poll() is None:  # Process is still running
                return jsonify({
                    'success': True,
                    'message': f'{kernel_type.title()} notebook is already running',
                    'url': f'http://localhost:{port}'
                })

        # Check if port is already in use by another process
        if is_port_in_use(port):
            return jsonify({
                'success': False,
                'error': f'Port {port} is already in use. Please stop any existing Jupyter servers on this port.'
            })

        # Prepare Jupyter command
        cmd = [
            'jupyter', 'notebook',
            '--ip=0.0.0.0',
            f'--port={port}',
            '--no-browser',
            '--allow-root',
            f'--notebook-dir={notebooks_dir.absolute()}',
            '--NotebookApp.token=""',
            '--NotebookApp.password=""',
            '--NotebookApp.allow_origin="*"'
        ]

        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=str(notebooks_dir.absolute()),
            env=os.environ.copy()
        )

        # Wait a moment for the server to start
        time.sleep(2)

        # Check if process started successfully
        if process.poll() is not None:
            # Process has already terminated
            stdout, stderr = process.communicate()
            return jsonify({
                'success': False,
                'error': f'Failed to start Jupyter server. Error: {stderr.decode()}'
            })

        notebook_processes[kernel_type] = process

        # Wait for server to be ready
        if wait_for_server_start(port, timeout=10):
            return jsonify({
                'success': True,
                'message': f'{kernel_type.title()} notebook started successfully',
                'url': f'http://localhost:{port}',
                'pid': process.pid
            })
        else:
            # Server didn't start in time, kill the process
            process.terminate()
            if kernel_type in notebook_processes:
                del notebook_processes[kernel_type]
            return jsonify({
                'success': False,
                'error': 'Jupyter server started but is not responding. Please try again.'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error starting notebook: {str(e)}'
        })

@notebooks_bp.route('/stop/<kernel_type>')
@login_required
def stop_notebook(kernel_type):
    """Stop Jupyter notebook for specified kernel"""
    try:
        if kernel_type not in notebook_processes:
            # Check if there's a process running on the port anyway
            port = notebook_ports.get(kernel_type)
            if port and is_port_in_use(port):
                return jsonify({
                    'success': False,
                    'error': f'Jupyter server is running on port {port} but not managed by Harmattan. Please stop it manually.'
                })
            return jsonify({
                'success': False,
                'error': f'No {kernel_type} notebook process found'
            })

        process = notebook_processes[kernel_type]

        # Terminate the process gracefully
        if process.poll() is None:  # Process is still running
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # Force kill if it doesn't terminate gracefully
                process.kill()
                process.wait()

        del notebook_processes[kernel_type]

        return jsonify({
            'success': True,
            'message': f'{kernel_type.title()} notebook stopped successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error stopping notebook: {str(e)}'
        })

@notebooks_bp.route('/status')
@login_required
def status():
    """Get status of running notebooks"""
    try:
        status_info = {}

        # Check all kernel types
        for kernel_type in ['python', 'r']:
            port = notebook_ports[kernel_type]

            if kernel_type in notebook_processes:
                process = notebook_processes[kernel_type]
                if process.poll() is None:  # Process is still running
                    status_info[kernel_type] = {
                        'running': True,
                        'pid': process.pid,
                        'url': f'http://localhost:{port}'
                    }
                else:
                    # Process has died, remove it from tracking
                    del notebook_processes[kernel_type]
                    status_info[kernel_type] = {
                        'running': False
                    }
            else:
                # Check if there's a server running on the port (not managed by us)
                if is_port_in_use(port):
                    status_info[kernel_type] = {
                        'running': True,
                        'pid': 'unknown',
                        'url': f'http://localhost:{port}',
                        'external': True
                    }
                else:
                    status_info[kernel_type] = {
                        'running': False
                    }

        return jsonify({
            'success': True,
            'status': status_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })
