from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from werkzeug.utils import secure_filename
import os
import pandas as pd
from pathlib import Path
from routes.auth import login_required
try:
    from models.database import DatabaseManager
except ImportError:
    from models.database_simple import DatabaseManager

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    """Main dashboard page"""
    db_manager = DatabaseManager()
    tables = db_manager.list_tables()
    return render_template('dashboard/index.html', tables=tables)

@dashboard_bp.route('/upload', methods=['POST'])
@login_required
def upload_file():
    """Handle file upload and conversion to parquet"""
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('dashboard.index'))
    
    file = request.files['file']
    table_name = request.form.get('table_name', '').strip().lower()
    
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('dashboard.index'))
    
    if not table_name:
        flash('Table name is required', 'error')
        return redirect(url_for('dashboard.index'))
    
    if file:
        try:
            filename = secure_filename(file.filename)
            file_path = os.path.join('uploads', filename)
            file.save(file_path)
            
            # Convert file to parquet and add to database
            db_manager = DatabaseManager()
            success = db_manager.add_table_from_file(file_path, table_name)
            
            if success:
                flash(f'Table "{table_name}" added successfully!', 'success')
                # Clean up uploaded file
                os.remove(file_path)
            else:
                flash('Error processing file', 'error')
                
        except Exception as e:
            flash(f'Error uploading file: {str(e)}', 'error')
    
    return redirect(url_for('dashboard.index'))

@dashboard_bp.route('/table/<table_name>')
@login_required
def view_table(table_name):
    """View table details"""
    db_manager = DatabaseManager()
    table_info = db_manager.get_table_info(table_name)
    sample_data = db_manager.get_sample_data(table_name, limit=100)
    
    return render_template('dashboard/table_view.html', 
                         table_name=table_name, 
                         table_info=table_info, 
                         sample_data=sample_data)

@dashboard_bp.route('/delete_table/<table_name>', methods=['POST'])
@login_required
def delete_table(table_name):
    """Delete a table"""
    try:
        db_manager = DatabaseManager()
        success = db_manager.delete_table(table_name)
        
        if success:
            flash(f'Table "{table_name}" deleted successfully!', 'success')
        else:
            flash('Error deleting table', 'error')
    except Exception as e:
        flash(f'Error deleting table: {str(e)}', 'error')
    
    return redirect(url_for('dashboard.index'))
