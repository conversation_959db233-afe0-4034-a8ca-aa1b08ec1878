#page-wrap {
    width: 800px;
    margin: 0 auto
}
table {
    border-collapse: collapse
}

table td,
table th {
    border: 1px solid #000;
    padding: 5px
}

#header {
    height: 15px;
    width: 100%;
    margin: 20px 0;
    background: #222;
    text-align: center;
    color: #fff;
    font: 700 15px Helvetica, Sans-Serif;
    text-decoration: uppercase;
    letter-spacing: 20px;
    padding: 8px 0
}

#address {
    width: 250px;
    height: 150px;
    float: left
}

#customer {
    overflow: hidden
}

#logo {
    text-align: right;
    float: right;
    position: relative;
    margin-top: 25px;
    border: 1px solid #fff;
    max-width: 540px;
    max-height: 100px;
    overflow: hidden
}

#logo:hover,
#logo.edit {
    border: 1px solid #000;
    margin-top: 0;
    max-height: 125px
}

#logoctr {
    display: none
}

#logo:hover #logoctr,
#logo.edit #logoctr {
    display: block;
    text-align: right;
    line-height: 25px;
    background: #eee;
    padding: 0 5px
}

#logohelp {
    text-align: left;
    display: none;
    font-style: italic;
    padding: 10px 5px
}

#logohelp input {
    margin-bottom: 5px
}

.edit #logohelp {
    display: block
}

.edit #save-logo,
.edit #cancel-logo {
    display: inline
}

.edit #image,
#save-logo,
#cancel-logo,
.edit #change-logo,
.edit #delete-logo {
    display: none
}

#customer-title {
    font-size: 20px;
    font-weight: 700;
    float: left
}

#meta {
    margin-top: 1px;
    width: 300px;
    float: right
}

#meta td {
    text-align: right
}

#meta td.meta-head {
    text-align: left;
    background: #eee
}

#meta td textarea {
    width: 100%;
    height: 20px;
    text-align: right
}

#items {
    clear: both;
    width: 100%;
    margin: 30px 0 0;
    border: 1px solid #000
}

#items th {
    background: #eee
}

#items textarea {
    width: 80px;
    height: 50px
}

#items tr.item-row td {
    border: 0;
    vertical-align: top
}

#items td.description {
    width: 300px
}

#items td.item-name {
    width: 175px
}

#items td.description textarea,
#items td.item-name textarea {
    width: 100%
}

#items td.total-line {
    border-right: 0;
    text-align: right
}

#items td.total-value {
    border-left: 0;
    padding: 10px
}

#items td.total-value textarea {
    height: 20px;
    background: 0 0
}

#items td.balance {
    background: #eee
}

#items td.blank {
    border: 0
}

#terms {
    text-align: center;
    margin: 20px 0 0
}

#terms h5 {
    text-transform: uppercase;
    font: 13px Helvetica, Sans-Serif;
    letter-spacing: 10px;
    border-bottom: 1px solid #000;
    padding: 0 0 8px;
    margin: 0 0 8px
}

#terms textarea {
    width: 100%;
    text-align: center
}

textarea:hover,
textarea:focus,
#items td.total-value textarea:hover,
#items td.total-value textarea:focus,
.delete:hover {
    background-color: #ef8
}

.delete-wpr {
    position: relative
}

.delete {
    display: block;
    color: #000;
    text-decoration: none;
    position: absolute;
    background: #eee;
    font-weight: 700;
    padding: 0 3px;
    border: 1px solid;
    top: -6px;
    left: -22px;
    font-family: Verdana;
    font-size: 12px
}

