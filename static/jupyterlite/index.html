<!DOCTYPE html>
<!--
  This is the root document for _your_ JupyterLite. Thanks for viewing your source!
     ___ __   __ _______ __   __ _______ _______ ______   ___     ___ _______ _______
    |   |  | |  |       |  | |  |       |       |    _ | |   |   |   |       |       |
    |   |  | |  |    _  |  |_|  |_     _|    ___|   | || |   |   |   |_     _|    ___|
    |   |  |_|  |   |_| |       | |   | |   |___|   |_||_|   |   |   | |   | |   |___
 ___|   |       |    ___|_     _| |   | |    ___|    __  |   |___|   | |   | |    ___|
|       |       |   |     |   |   |   | |   |___|   |  | |       |   | |   | |   |___
|_______|_______|___|     |___|   |___| |_______|___|  |_|_______|___| |___| |_______|

Code: https://github.com/jupyterlite/jupyterlite
Demo: https://jupyterlite.rtfd.io/en/latest/try

# FILES

Your JupyterLite is structured like this:

    index.html             <---- you are here
    {:app}/                <---- currently-known: `lab` (semi-required) and `notebook`
      index.html
      extensions/
        {:org}/{:package}/ <---|
        {:package}/        <---+- federated extensions
          schema/
          package.json
    # TBD
    files/                 <----- also need to populate `api/contents/`!
    static/                <----- might save duplication
      {:app}

-->
<html>
  <head>
    <!--
      # jupyter-config-data

      The JSON in `jupyter-lite.json` is the last that will be loaded by most
      child `index.html` pages, and every field can be overridden.

      See the recognized schema as described in the documentation:

      - https://jupyterlite.rtfd.io/en/latest/reference/schema.html

      Notes
        - fields that...
          - are relative paths values
            - will have the parent directory of _this_ file appended, accounting
              for "magic" hosts which redirect to `{:path}index.html` to `{:path}`,
              with or without a slash
        - some fields, such as `federated_extensions` will be _added together_
    -->
    <script id="jupyter-config-data" type="application/json" data-jupyter-lite-root=".">
      {}
    </script>
    <!--
      # jupyter-lite-root

      The source of truth for relative path locations. The `.` means _this_
      is the root, and will redirect to the `appUrl` defined in
      `.jupyter-lite/#jupyter-config-data`.
    -->
    <script type="module">
      await import('./config-utils.js');
    </script>
    <!-- that's it, there shouldn't be anything below here in head! -->
  </head>
</html>
