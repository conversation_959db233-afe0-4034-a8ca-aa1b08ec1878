{"cells": [{"cell_type": "markdown", "id": "dea14a34-4803-44da-ae14-88b2c87a6f12", "metadata": {}, "source": ["# jupyter-lite.ipynb\n", "\n", "This notebook is the preferred source of site-specific runtime [configuration](https://jupyterlite.readthedocs.io/en/latest/configuring.html) for a JupyterLite app, and will override any configuration in [`jupyter-lite.json`](./jupyter-lite.json)."]}, {"cell_type": "markdown", "id": "6d19715a-0a8c-4600-9ff1-e82ba667e2d3", "metadata": {}, "source": ["## Editing Configuration\n", "\n", "The configuration is stored in this Notebook's metadata under the `jupyter-lite` key. To edit the configuration in JupyterLab.\n", "\n", "- open the _Property Inspector_ sidebar\n", "- expand the _Advanced Tools_ section\n", "- edit the `jupyter-lite` metadata sub-key\n", "- press the \"check\" icon\n", "- save the notebook"]}], "metadata": {"jupyter-lite": {"jupyter-config-data": {}, "jupyter-lite-schema-version": 0}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}}, "nbformat": 4, "nbformat_minor": 5}