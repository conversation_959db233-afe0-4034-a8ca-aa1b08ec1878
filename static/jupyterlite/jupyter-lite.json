{"jupyter-config-data": {"appName": "JupyterLite", "appUrl": "./lab", "appVersion": "0.6.3", "baseUrl": "./", "defaultKernelName": "python", "faviconUrl": "./lab/favicon.ico", "federated_extensions": [{"extension": "./extension", "load": "static/remoteEntry.b2792fe643cae74ea824.js", "name": "@jupyter-notebook/lab-extension", "style": "./style"}, {"extension": "./extension", "load": "static/remoteEntry.34d85833e6f4e142fc64.js", "name": "@jupyterlite/xeus-extension", "style": "./style"}, {"extension": "./extension", "load": "static/remoteEntry.aa1060b2d1221f8e5688.js", "name": "jupyterlab_pygments", "style": "./style"}], "fileTypes": {"css": {"extensions": [".css"], "fileFormat": "text", "mimeTypes": ["text/css"], "name": "css"}, "csv": {"extensions": [".csv"], "fileFormat": "text", "mimeTypes": ["text/csv"], "name": "csv"}, "fasta": {"extensions": [".fasta"], "fileFormat": "text", "mimeTypes": ["text/plain"], "name": "fasta"}, "geojson": {"extensions": [".geo<PERSON><PERSON>"], "fileFormat": "json", "mimeTypes": ["application/geo+json"], "name": "g<PERSON><PERSON><PERSON>"}, "gzip": {"extensions": [".tgz", ".gz", ".gzip"], "fileFormat": "base64", "mimeTypes": ["application/gzip"], "name": "gzip"}, "html": {"extensions": [".html"], "fileFormat": "text", "mimeTypes": ["text/html"], "name": "html"}, "ical": {"extensions": [".ical", ".ics", ".ifb", ".icalendar"], "fileFormat": "text", "mimeTypes": ["text/calendar"], "name": "ical"}, "ico": {"extensions": [".ico"], "fileFormat": "base64", "mimeTypes": ["image/x-icon"], "name": "ico"}, "ipynb": {"extensions": [".ipynb"], "fileFormat": "json", "mimeTypes": ["application/x-ipynb+json"], "name": "ipynb"}, "jpeg": {"extensions": [".jpeg", ".jpg"], "fileFormat": "base64", "mimeTypes": ["image/jpeg"], "name": "jpeg"}, "js": {"extensions": [".js", ".mjs"], "fileFormat": "text", "mimeTypes": ["application/javascript"], "name": "js"}, "jsmap": {"extensions": [".map"], "fileFormat": "json", "mimeTypes": ["application/json"], "name": "jsmap"}, "json": {"extensions": [".json"], "fileFormat": "json", "mimeTypes": ["application/json"], "name": "json"}, "manifest": {"extensions": [".manifest"], "fileFormat": "text", "mimeTypes": ["text/cache-manifest"], "name": "manifest"}, "md": {"extensions": [".md", ".markdown"], "fileFormat": "text", "mimeTypes": ["text/markdown"], "name": "md"}, "pdf": {"extensions": [".pdf"], "fileFormat": "base64", "mimeTypes": ["application/pdf"], "name": "pdf"}, "plain": {"extensions": [".txt"], "fileFormat": "text", "mimeTypes": ["text/plain"], "name": "plain"}, "png": {"extensions": [".png"], "fileFormat": "base64", "mimeTypes": ["image/png"], "name": "png"}, "py": {"extensions": [".py"], "fileFormat": "text", "mimeTypes": ["text/x-python", "application/x-python-code"], "name": "py"}, "svg": {"extensions": [".svg"], "fileFormat": "text", "mimeTypes": ["image/svg+xml"], "name": "svg"}, "toml": {"extensions": [".toml"], "fileFormat": "text", "mimeTypes": ["application/toml"], "name": "toml"}, "vue": {"extensions": [".vue"], "fileFormat": "text", "mimeTypes": ["text/plain"], "name": "vue"}, "wasm": {"extensions": [".wasm"], "fileFormat": "base64", "mimeTypes": ["application/wasm"], "name": "wasm"}, "wheel": {"extensions": [".whl"], "fileFormat": "base64", "mimeTypes": ["octet/stream", "application/x-wheel+zip"], "name": "wheel"}, "xml": {"extensions": [".xml"], "fileFormat": "text", "mimeTypes": ["application/xml"], "name": "xml"}, "yaml": {"extensions": [".yaml", ".yml"], "fileFormat": "text", "mimeTypes": ["application/x-yaml"], "name": "yaml"}}, "fullLabextensionsUrl": "./extensions", "fullStaticUrl": "./build", "licensesUrl": "./lab/api/licenses"}, "jupyter-lite-schema-version": 0}