"use strict";(self.webpackChunk_jupyter_notebook_lab_extension=self.webpackChunk_jupyter_notebook_lab_extension||[]).push([[568],{568:(e,o,n)=>{n.r(o),n.d(o,{default:()=>m});var t,a=n(418),c=n(861),r=n(614),l=n(408),i=n(505),s=n(778),b=n(403),d=n(800);!function(e){e.launchNotebookTree="jupyter-notebook:launch-tree",e.openNotebook="jupyter-notebook:open-notebook",e.openLab="jupyter-notebook:open-lab",e.openNbClassic="jupyter-notebook:open-nbclassic"}(t||(t={}));const u={id:"@jupyter-notebook/lab-extension:interface-switcher",autoStart:!0,requires:[i.ITranslator,l.INotebookTracker],optional:[c<PERSON><PERSON>,b.<PERSON>otebook<PERSON>ath<PERSON><PERSON>er,b.INotebookShell,a.ILabShell,c.IToolbarWidgetRegistry],activate:(e,o,n,a,c,l,i,u)=>{const{commands:m,shell:p}=e,k=r.PageConfig.getBaseUrl(),h=o.load("notebook"),_="true"===r.PageConfig.getOption("nbclassic_enabled"),f=new s.Menu({commands:m}),g=[],N=null!=c?c:b.defaultNotebookPathOpener;l||g.push({command:t.openNotebook,commandLabel:h.__("Notebook"),commandDescription:h.__("Open in %1","Jupyter Notebook"),buttonLabel:"openNotebook",urlPrefix:`${k}tree`}),i||g.push({command:t.openLab,commandLabel:h.__("JupyterLab"),commandDescription:h.__("Open in %1","JupyterLab"),buttonLabel:"openLab",urlPrefix:`${k}doc/tree`}),_&&g.push({command:t.openNbClassic,commandLabel:h.__("NbClassic"),commandDescription:h.__("Open in %1","NbClassic"),buttonLabel:"openNbClassic",urlPrefix:`${k}nbclassic/notebooks`});const C=()=>null!==n.currentWidget&&n.currentWidget===p.currentWidget;let L;if(g.forEach((e=>{const{command:o}=e;(e=>{const{command:o,commandLabel:t,commandDescription:c,urlPrefix:r}=e;m.addCommand(o,{label:e=>(e.noLabel,e.isMenu||e.isPalette?c:t),caption:t,execute:()=>{const e=n.currentWidget;e&&N.open({prefix:r,path:e.context.path})},isEnabled:C}),a&&a.addItem({command:o,category:"Other",args:{isPalette:!0}})})(e),f.addItem({command:o})})),1===g.length)L=e=>{const o=new d.CommandToolbarButton({commands:m,id:g[0].command,label:g[0].commandLabel,icon:d.launchIcon});return o.addClass("jp-nb-interface-switcher-button"),o};else{const e={overflowMenuOptions:{isVisible:!1}},o=new s.MenuBar(e);f.title.label=h.__("Open in..."),f.title.icon=d.caretDownIcon,o.addMenu(f),L=o=>{const n=new s.MenuBar(e);return n.addMenu(f),n.addClass("jp-InterfaceSwitcher"),n}}u&&u.addFactory("Notebook","interfaceSwitcher",L)}},m=[{id:"@jupyter-notebook/lab-extension:launch-tree",autoStart:!0,requires:[i.ITranslator],optional:[c.ICommandPalette],activate:(e,o,n)=>{const{commands:a}=e,c=o.load("notebook"),l=c.__("Help");a.addCommand(t.launchNotebookTree,{label:c.__("Launch Jupyter Notebook File Browser"),execute:()=>{const e=r.URLExt.join(r.PageConfig.getBaseUrl(),"tree");window.open(e)}}),n&&n.addItem({command:t.launchNotebookTree,category:l})}},u]}}]);