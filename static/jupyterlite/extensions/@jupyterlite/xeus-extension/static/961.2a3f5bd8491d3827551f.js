"use strict";(self.webpackChunk_jupyterlite_xeus_extension=self.webpackChunk_jupyterlite_xeus_extension||[]).push([[961],{961:(e,n,t)=>{t.r(n),t.d(n,{IEmpackEnvMetaFile:()=>c,default:()=>g});var s=t(240),a=t(367),r=t(234),o=t(696),i=t(269);const c=new(t(262).Token)("@jupyterlite/xeus:IEmpackEnvMetaFile");async function l(e){const n=a.URLExt.join(a.PageConfig.getBaseUrl(),e),t=await fetch(n,{method:"GET"});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);return await t.json()}const u={id:"@jupyterlite/xeus-kernel:register",autoStart:!0,requires:[o.IKernelSpecs],optional:[r.IServiceWorkerManager,c,s.ILoggerRegistry],activate:async(e,n,t,s,r)=>{let o=[];try{o=await l("xeus/kernels.json")}catch(e){throw console.log(`Could not fetch xeus/kernels.json: ${e}`),e}const c=e.serviceManager.contents,u=o.map((e=>e.kernel)),g=u.filter(((e,n)=>u.indexOf(e)!==n));for(const e of o){const{env_name:r,kernel:o}=e,u=await l(`xeus/${r}/${o}/kernel.json`);u.name=o,u.dir=o,u.envName=r,g.includes(o)&&(u.name=`${o} (${r})`,u.display_name=`${u.display_name} [${r}]`);for(const[e,n]of Object.entries(u.resources))u.resources[e]=a.URLExt.join(a.PageConfig.getBaseUrl(),n);n.register({spec:u,create:async e=>{const n=u.name.indexOf(" ");n>0&&(u.name=u.name.slice(0,n));const a=!(!(null==t?void 0:t.enabled)&&!crossOriginIsolated);a?console.info(`${u.name} contents will be synced with Jupyter Contents`):console.warn(`${u.name} contents will NOT be synced with Jupyter Contents`);const r=s?await s.getLink(u):"";return new i.WebWorkerKernel({...e,contentsManager:c,mountDrive:a,kernelSpec:u,empackEnvMetaLink:r,browsingContextId:(null==t?void 0:t.browsingContextId)||""})}})}await e.serviceManager.kernelspecs._specsChanged.emit(e.serviceManager.kernelspecs.specs),r&&(new BroadcastChannel("/xeus-kernel-logs-broadcast").onmessage=n=>{var t;const{kernelId:s,payload:a}=n.data,{sessions:o}=e.serviceManager;let i="";for(const e of o.running())if((null===(t=e.kernel)||void 0===t?void 0:t.id)===s){i=e.path;break}r.getLogger(i).log(a)})}},g=[{id:"@jupyterlite/xeus:empack-env-meta",autoStart:!0,provides:c,activate:()=>({getLink:async e=>{const{envName:n}=e;return`${a.URLExt.join(a.PageConfig.getBaseUrl(),`xeus/${n}`)}`}})},u]}}]);