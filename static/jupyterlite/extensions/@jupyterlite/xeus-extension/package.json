{"name": "@jupyterlite/xeus-extension", "version": "4.0.5", "description": "JupyterLite loader for Xeus kernels", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlite/xeus", "bugs": {"url": "https://github.com/jupyterlite/xeus/issues"}, "license": "BSD-3-<PERSON><PERSON>", "author": "JupyterLite Contributors", "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,js,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "schema/*.json"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "repository": {"type": "git", "url": "https://github.com/jupyterlite/xeus.git"}, "scripts": {"build": "jlpm build:lib && jlpm build:labextension:dev", "build:prod": "jlpm clean && jlpm build:lib:prod  && jlpm build:labextension", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:lib": "tsc --sourceMap", "build:lib:prod": "tsc", "clean": "jlpm clean:lib", "clean:lib": "rimraf lib tsconfig.tsbuildinfo", "clean:labextension": "rimraf jupyterlite_xeus/labextension jupyterlite_xeus/_version.py", "clean:all": "jlpm clean:lib && jlpm clean:labextension", "watch": "run-p watch:src watch:labextension", "watch:src": "tsc -w --sourceMap", "watch:labextension": "jupyter labextension watch ."}, "dependencies": {"@jupyterlab/application": "^4.4.2", "@jupyterlab/coreutils": "^6.4.2", "@jupyterlab/logconsole": "^4.4.2", "@jupyterlab/notebook": "^4.4.2", "@jupyterlite/contents": "^0.6.0", "@jupyterlite/kernel": "^0.6.0", "@jupyterlite/server": "^0.6.0", "@jupyterlite/xeus": "^4.0.5", "@lumino/coreutils": "^2"}, "devDependencies": {"@jupyterlab/builder": "^4.4.2", "@types/json-schema": "^7.0.11", "@types/react": "^18.0.26", "@types/react-addons-linked-state-mixin": "^0.14.22", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.7.1", "npm-run-all": "^4.1.5", "rimraf": "^5.0.1", "source-map-loader": "^1.0.2", "ts-loader": "^9.2.6", "typescript": "^5.5", "webpack": "^5.87.0", "yjs": "^13.5.0"}, "publishConfig": {"access": "public"}, "sideEffects": ["style/*.css", "style/index.js"], "styleModule": "style/index.js", "jupyterlab": {"extension": true, "outputDir": "../../jupyterlite_xeus/labextension", "webpackConfig": "lab.webpack.config.js", "sharedPackages": {"@jupyterlite/kernel": {"bundled": false, "singleton": true}, "@jupyterlite/server": {"bundled": false, "singleton": true}, "@jupyterlite/contents": {"bundled": false, "singleton": true}}, "_build": {"load": "static/remoteEntry.34d85833e6f4e142fc64.js", "extension": "./extension", "style": "./style"}}}