{"name": "jupyterlab_pygments", "version": "0.2.2", "description": "Pygments theme using JupyterLab CSS variables", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab_pygments", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab_pygments/issues"}, "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "Jupyter Development Team", "email": "<EMAIL>"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,js,eot,gif,html,jpg,json,png,svg,woff2,ttf}"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab_pygments.git"}, "scripts": {"build": "jlpm build:css && jlpm build:lib && jlpm build:labextension:dev", "build:prod": "jlpm clean && jlpm build:css && jlpm build:lib && jlpm build:labextension", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:lib": "tsc", "build:css": "python generate_css.py", "clean": "jlpm clean:lib", "clean:lib": "rimraf lib tsconfig.tsbuildinfo style/base.css", "clean:lintcache": "rimraf .eslintcache .stylelintcache", "clean:labextension": "rimraf jupyterlab_pygments/labextension", "clean:all": "jlpm clean:lib && jlpm clean:labextension && jlpm clean:lintcache", "eslint": "jlpm eslint:check --fix", "eslint:check": "eslint . --cache --ext .ts,.tsx", "install:extension": "jlpm build", "lint": "jlpm stylelint && jlpm prettier && jlpm eslint", "lint:check": "jlpm stylelint:check && jlpm prettier:check && jlpm eslint:check", "prettier": "jlpm prettier:base --write --list-different", "prettier:base": "prettier \"**/*{.ts,.tsx,.js,.jsx,.css,.json,.md}\"", "prettier:check": "jlpm prettier:base --check", "stylelint": "jlpm stylelint:check --fix", "stylelint:check": "stylelint --cache \"style/**/*.css\"", "watch": "run-p watch:src watch:labextension", "watch:src": "tsc -w", "watch:labextension": "jupyter labextension watch ."}, "dependencies": {"@jupyterlab/application": "^3.1.0"}, "devDependencies": {"@jupyterlab/builder": "^3.1.0", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "eslint": "^7.14.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "npm-run-all": "^4.1.5", "prettier": "^2.1.1", "rimraf": "^3.0.2", "stylelint": "^14.3.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^6.0.0", "stylelint-config-standard": "~24.0.0", "stylelint-prettier": "^2.0.0", "typescript": "~4.1.3"}, "sideEffects": ["style/*.css", "style/index.js"], "styleModule": "style/index.js", "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "outputDir": "jupyterlab_pygments/labextension", "_build": {"load": "static/remoteEntry.aa1060b2d1221f8e5688.js", "extension": "./extension", "style": "./style"}}, "jupyter-releaser": {"hooks": {"before-build-npm": ["python -m pip install jupyterlab~=3.1", "jlpm"], "before-build-python": ["jlpm clean:all"]}}}