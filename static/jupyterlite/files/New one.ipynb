{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# New one\n", "\n", "This notebook was created in Harmattan Analytics Platform.\n", "\n", "## Getting Started\n", "\n", "You can access your Harmattan database using:\n", "\n", "```python\n", "import duckdb\n", "conn = duckdb.connect('../dbs/harmattan.db')\n", "df = conn.execute('SELECT * FROM your_table').df()\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "print('Welcome to Harmattan Analytics!')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 4}